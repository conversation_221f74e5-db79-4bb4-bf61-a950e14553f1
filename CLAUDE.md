# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is OmniRAG, an advanced Retrieval-Augmented Generation (RAG) system with a Streamlit web interface. The project provides a complete RAG pipeline supporting multiple document types, embedding models, and vector databases with AI-powered question answering.

## Commands

### Development Environment

```bash
# Install and setup environment
bash install.sh

# Activate virtual environment
source .venv/bin/activate

# Run the main application
python main.py

# Run specific Python version (3.11)
python3.11 main.py
```

### Testing

```bash
# Run tests with pytest
pytest tests/

# Run specific test file
pytest tests/test_trivial_check.py

# Run with coverage
pytest --cov=src tests/
```

### Python Environment

The project uses Python 3.11 with a virtual environment created by `install.sh`. The main Python executable is at `/Users/<USER>/code/datategy/omnirag/.venv/bin/python`.

## High-Level Architecture

### Core Components

1. **RAG Pipeline** (`src/ragger.py:1`): Main Streamlit application that provides the web interface for document upload, vector store management, and chat interactions.

2. **Document Processing** (`src/docloader.py`, `src/chunker.py`): Handles loading various document types (PDF, Word, Excel, etc.) and chunking them for embedding.

3. **Embedding System** (`src/embedding.py`): Manages vector embeddings using sentence transformers and supports multiple vector databases (FAISS, Chroma, Weaviate).

4. **LLM Integration** (`src/customchain.py`, `src/customchain_naive.py`): Implements different RAG strategies including HAH (Hybrid Approach Heuristics), C-HAH Composite, and Naive RAG.

5. **Model Management** (`src/modeltokenizer.py`): Handles loading and managing LLM models with GPU/CPU auto-selection.

6. **Metadata Extraction** (`src/metadata_extraction/`): Comprehensive system for extracting metadata from various document types including PDFs, images, Office documents, and structured data.

### Key Design Patterns

- **Pipeline Types**: Three main RAG implementations - C-HAH RAG (composite), HAH RAG (basic), and Naive RAG, selectable via `PipelineType` enum.
- **Modular Architecture**: Each component is designed to be independent with clear interfaces (e.g., `EmbeddingVectors`, `TextChunker`, `CustomLLMChain`).
- **GPU/CPU Auto-Selection**: Automatic detection and configuration of computational resources via `GPUSelector`.
- **Metadata-Driven**: Comprehensive metadata extraction and filtering system for better document retrieval.
- **Session Management**: Persistent chat history with SQLite database and in-memory session state.

### Configuration System

- **Environment-based**: Uses `pydantic_settings` for configuration management
- **Hardware-aware**: Automatically adjusts based on available GPU memory and CPU capabilities
- **Multiple backends**: Supports different processing backends (GPU, CPU, forced configurations)

### Document Support

The system handles multiple document formats including:
- PDF (with OCR support via Tesseract)
- Microsoft Office (Word, Excel, PowerPoint)
- Images (with OCR)
- OpenDocument formats
- HTML/XML files
- Text files

### Vector Store Options

- **FAISS**: High-performance similarity search (`vector_store/faiss_*`)
- **Chroma**: Persistent vector database
- **Weaviate**: Cloud-native vector database (in `weaviate/` directory)

### Model Ecosystem

Supports multiple quantized LLM models including:
- Llama 3.1 series (8B, 70B, 405B)
- Llama 3 series (8B, 70B)
- Mistral variants
- Gemma 2
- Specialized CPU models (Llama 3.2 3B GGUF)

### Critical Features

1. **Metadata Filtering**: Advanced filtering capabilities that compile after context chain initialization
2. **Conversation Memory**: Persistent chat history with conversation memory buffers
3. **Performance Metrics**: Built-in evaluation metrics and performance tracking
4. **Multi-language Support**: Supports English and French with configurable reasoning instructions
5. **Benchmarking Tools**: Complete benchmarking suite in `benchmarking/` directory

### Data Flow

1. Document upload → Metadata extraction → Text chunking
2. Embedding generation → Vector storage → Index creation
3. User query → Metadata filtering → Context retrieval
4. LLM reasoning → Response generation → Metrics collection

## Important Notes

- Uses PEP8 code style with NumPy docstring conventions
- Follows conventional commit standards for version control
- Requires SSH key for repository access (security measure)
- GPU memory requirements vary significantly by model (2GB to 200GB)
- Metadata filtering is only available after context chain initialization
- The system automatically handles model quantization and optimization