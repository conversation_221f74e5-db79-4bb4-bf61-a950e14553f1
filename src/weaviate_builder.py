"""
Enhanced Weaviate Index Builder for OmniRAG

This module provides utilities to build Weaviate indices from various document sources
with support for both v3 and v4 APIs and flexible document processing.
"""

import asyncio
import logging
import re
import warnings
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

import docx
from pypdf import PdfReader

from src.weaviate_integration import WeaviateManager

# Silence warnings
warnings.filterwarnings("ignore", category=UserWarning)

logger = logging.getLogger(__name__)


class WeaviateIndexBuilder:
    """Build Weaviate indices from various document sources"""
    
    def __init__(self, host: str = "localhost", port: int = 8080):
        """Initialize the index builder
        
        Parameters
        ----------
        host : str, optional
            Weaviate host, by default "localhost"
        port : int, optional
            Weaviate port, by default 8080
        """
        self.weaviate_manager = WeaviateManager(host=host, port=port)
        self.connected = False
    
    def connect(self) -> bool:
        """Connect to Weaviate
        
        Returns
        -------
        bool
            True if connection successful
        """
        self.connected = self.weaviate_manager.connect()
        return self.connected
    
    def disconnect(self):
        """Disconnect from Weaviate"""
        self.weaviate_manager.disconnect()
        self.connected = False
    
    def create_collection(
        self, 
        collection_name: str = "Question",
        vectorizer_model: str = "nomic-embed-text",
        ollama_endpoint: str = "http://ollama:11434",
        recreate: bool = False
    ) -> bool:
        """Create a Weaviate collection for document storage
        
        Parameters
        ----------
        collection_name : str, optional
            Name of the collection, by default "Question"
        vectorizer_model : str, optional
            Embedding model to use, by default "nomic-embed-text"
        ollama_endpoint : str, optional
            Ollama API endpoint, by default "http://ollama:11434"
        recreate : bool, optional
            Whether to recreate if exists, by default False
            
        Returns
        -------
        bool
            True if creation successful
        """
        if not self.connected:
            logger.error("Not connected to Weaviate")
            return False
        
        # Delete existing collection if recreate is True
        if recreate:
            self.weaviate_manager.delete_collection(collection_name)
        
        # Create vectorizer config for v4 API
        if self.weaviate_manager.api_version == "v4":
            from weaviate.classes.config import Configure
            vectorizer_config = Configure.Vectors.text2vec_ollama(
                api_endpoint=ollama_endpoint,
                model=vectorizer_model,
            )
        else:
            # For v3 API, we'll use "none" vectorizer and handle embeddings externally
            vectorizer_config = {"vectorizer": "none"}
        
        return self.weaviate_manager.create_collection(
            collection_name=collection_name,
            vectorizer_config=vectorizer_config
        )
    
    def chunk_text(
        self, 
        text: str, 
        chunk_size: int = 200, 
        overlap: int = 20,
        method: str = "word"
    ) -> List[str]:
        """Chunk text into smaller segments
        
        Parameters
        ----------
        text : str
            Text to chunk
        chunk_size : int, optional
            Size of each chunk, by default 200
        overlap : int, optional
            Overlap between chunks, by default 20
        method : str, optional
            Chunking method ("word" or "sentence"), by default "word"
            
        Returns
        -------
        List[str]
            List of text chunks
        """
        if method == "word":
            words = re.findall(r"\S+", text)
            chunks = []
            
            for i in range(0, len(words), chunk_size - overlap):
                chunk_words = words[i:i + chunk_size]
                if chunk_words:
                    chunks.append(" ".join(chunk_words))
            
            return [chunk for chunk in chunks if chunk.strip()]
        
        elif method == "sentence":
            # Simple sentence splitting
            sentences = re.split(r'[.!?]+', text)
            chunks = []
            current_chunk = []
            current_size = 0
            
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                
                sentence_words = len(sentence.split())
                
                if current_size + sentence_words > chunk_size and current_chunk:
                    chunks.append(" ".join(current_chunk))
                    # Keep overlap
                    if overlap > 0 and len(current_chunk) > overlap:
                        current_chunk = current_chunk[-overlap:]
                        current_size = sum(len(s.split()) for s in current_chunk)
                    else:
                        current_chunk = []
                        current_size = 0
                
                current_chunk.append(sentence)
                current_size += sentence_words
            
            if current_chunk:
                chunks.append(" ".join(current_chunk))
            
            return chunks
        
        else:
            raise ValueError(f"Unknown chunking method: {method}")
    
    def process_pdf(self, pdf_path: Union[str, Path]) -> List[Dict[str, Any]]:
        """Process a PDF file into document chunks

        Parameters
        ----------
        pdf_path : Union[str, Path]
            Path to PDF file

        Returns
        -------
        List[Dict[str, Any]]
            List of document chunks with metadata
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            logger.error(f"PDF file not found: {pdf_path}")
            return []

        logger.info(f"Processing PDF: {pdf_path}")

        reader = PdfReader(str(pdf_path))
        num_pages = len(reader.pages)

        full_text_parts = []
        for page_idx, page in enumerate(reader.pages, start=1):
            text = page.extract_text() or ""
            full_text_parts.append(text)

            if page_idx % 5 == 0 or page_idx == num_pages:
                logger.info(f"Extracted page {page_idx}/{num_pages}")

        full_text = "\n".join(full_text_parts)
        chunks = self.chunk_text(full_text, chunk_size=200)

        documents = []
        for i, chunk in enumerate(chunks):
            if chunk.strip():
                documents.append({
                    "text": chunk,
                    "chunk_index": i,
                    "source": str(pdf_path.name),
                    "document_type": "pdf",
                    "total_chunks": len(chunks)
                })

        logger.info(f"Created {len(documents)} chunks from PDF")
        return documents
    
    def process_text_file(self, text_path: Union[str, Path]) -> List[Dict[str, Any]]:
        """Process a text file into document chunks

        Parameters
        ----------
        text_path : Union[str, Path]
            Path to text file

        Returns
        -------
        List[Dict[str, Any]]
            List of document chunks with metadata
        """
        text_path = Path(text_path)
        if not text_path.exists():
            logger.error(f"Text file not found: {text_path}")
            return []

        logger.info(f"Processing text file: {text_path}")

        with open(text_path, 'r', encoding='utf-8') as f:
            full_text = f.read()

        chunks = self.chunk_text(full_text, chunk_size=200)

        documents = []
        for i, chunk in enumerate(chunks):
            if chunk.strip():
                documents.append({
                    "text": chunk,
                    "chunk_index": i,
                    "source": str(text_path.name),
                    "document_type": "text",
                    "total_chunks": len(chunks)
                })

        logger.info(f"Created {len(documents)} chunks from text file")
        return documents
    
    def process_documents(self, documents: List[str]) -> List[Dict[str, Any]]:
        """Process a list of text documents into chunks
        
        Parameters
        ----------
        documents : List[str]
            List of document texts
            
        Returns
        -------
        List[Dict[str, Any]]
            List of document chunks with metadata
        """
        all_chunks = []
        
        for doc_idx, doc_text in enumerate(documents):
            chunks = self.chunk_text(doc_text, chunk_size=200)
            
            for chunk_idx, chunk in enumerate(chunks):
                if chunk.strip():
                    all_chunks.append({
                        "text": chunk,
                        "chunk_index": chunk_idx,
                        "document_index": doc_idx,
                        "source": f"document_{doc_idx}",
                        "document_type": "text",
                        "total_chunks": len(chunks)
                    })
        
        logger.info(f"Created {len(all_chunks)} chunks from {len(documents)} documents")
        return all_chunks
    
    async def build_index(
        self, 
        documents: List[Dict[str, Any]], 
        collection_name: str = "Question",
        batch_size: int = 50
    ) -> bool:
        """Build Weaviate index from processed documents
        
        Parameters
        ----------
        documents : List[Dict[str, Any]]
            List of processed documents
        collection_name : str, optional
            Collection name, by default "Question"
        batch_size : int, optional
            Batch size for ingestion, by default 50
            
        Returns
        -------
        bool
            True if indexing successful
        """
        if not self.connected:
            logger.error("Not connected to Weaviate")
            return False
        
        if not documents:
            logger.warning("No documents to index")
            return True
        
        logger.info(f"Indexing {len(documents)} documents to collection '{collection_name}'")
        
        successful, failed = self.weaviate_manager.ingest_documents(
            documents=documents,
            collection_name=collection_name,
            batch_size=batch_size
        )
        
        if failed > 0:
            logger.warning(f"Failed to index {failed} documents")
            return False
        
        logger.info(f"Successfully indexed {successful} documents")
        return True
