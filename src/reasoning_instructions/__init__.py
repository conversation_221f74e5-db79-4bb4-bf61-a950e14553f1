from enum import StrEnum

from .en import NAIVE_INSTRUCTION as EN<PERSON>NAIVE_INSTRUCTION
from .en import PROMPT_PHRASES_TO_REMOVE as EN_PROMPT_PHRASES_TO_REMOVE
from .en import PROMPT_SECTIONS_TO_REMOVE as EN_PROMPT_SECTIONS_TO_REMOVE
from .en import REASONING_INSTRUCTIONS as EN_REASONING_INSTRUCTIONS
from .en import REASONING_PATTERNS as EN_REASONING_PATTERNS
from .fr import NAIVE_INSTRUCTION as FR_NAIVE_INSTRUCTION
from .fr import PROMPT_PHRASES_TO_REMOVE as FR_PROMPT_PHRASES_TO_REMOVE
from .fr import PROMPT_SECTIONS_TO_REMOVE as FR_PROMPT_SECTIONS_TO_REMOVE
from .fr import REASONING_INSTRUCTIONS as FR_REASONING_INSTRUCTIONS
from .fr import REASONING_PATTERNS as FR_REASONING_PATTERNS
from .en import TRIVIAL_TEMPLATE as EN_TRIVIAL_TEMPLATE
from .fr import TRIVIAL_TEMPLATE as FR_TRIVIAL_TEMPLATE


class InstructionLangs(StrEnum):
    EN = "EN"
    FR = "FR"

INSTRUCTIONS_LANGS_LIST = list(map(str, InstructionLangs))
DEFAULT_INSTRUCTION_LANG = InstructionLangs.EN

ALL_REASONING_PATTERNS = {
    InstructionLangs.EN: EN_REASONING_PATTERNS,
    InstructionLangs.FR: FR_REASONING_PATTERNS,
}
ALL_REASONING_INSTRUCTIONS = {
    InstructionLangs.EN: EN_REASONING_INSTRUCTIONS,
    InstructionLangs.FR: FR_REASONING_INSTRUCTIONS,
}
ALL_NAIVE_INSTRUCTIONS = {
    InstructionLangs.EN: EN_NAIVE_INSTRUCTION,
    InstructionLangs.FR: FR_NAIVE_INSTRUCTION,
}
ALL_PROMPT_SECTIONS_TO_REMOVE = {
    InstructionLangs.EN: EN_PROMPT_SECTIONS_TO_REMOVE,
    InstructionLangs.FR: FR_PROMPT_SECTIONS_TO_REMOVE,
}
ALL_PROMPT_PHRASES_TO_REMOVE = {
    InstructionLangs.EN: EN_PROMPT_PHRASES_TO_REMOVE,
    InstructionLangs.FR: FR_PROMPT_PHRASES_TO_REMOVE,
}

# Mapping of trivial templates by language (mirrors the pattern of other constants)
TRIVIAL_TEMPLATES = {
    InstructionLangs.EN: EN_TRIVIAL_TEMPLATE,
    InstructionLangs.FR: FR_TRIVIAL_TEMPLATE,
}

__all__ = (
    "InstructionLangs",
    "INSTRUCTIONS_LANGS_LIST",
    "DEFAULT_INSTRUCTION_LANG",
    "ALL_REASONING_PATTERNS",
    "ALL_REASONING_INSTRUCTIONS",
    "ALL_PROMPT_SECTIONS_TO_REMOVE",
    "ALL_PROMPT_PHRASES_TO_REMOVE",
    "TRIVIAL_TEMPLATES",
)
