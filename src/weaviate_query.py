"""
Enhanced Weaviate Query Interface for OmniRAG

This module provides utilities to query Weaviate indices with support for both
text and vector queries, compatible with v3 and v4 APIs.
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Optional, Union
import numpy as np

# Import the new Weaviate integration
from src.weaviate_integration import WeaviateManager

logger = logging.getLogger(__name__)


class WeaviateQueryInterface:
    """Interface for querying Weaviate indices"""
    
    def __init__(self, host: str = "localhost", port: int = 8080):
        """Initialize the query interface
        
        Parameters
        ----------
        host : str, optional
            Weaviate host, by default "localhost"
        port : int, optional
            Weaviate port, by default 8080
        """
        self.weaviate_manager = WeaviateManager(host=host, port=port)
        self.connected = False
    
    def connect(self) -> bool:
        """Connect to Weaviate
        
        Returns
        -------
        bool
            True if connection successful
        """
        self.connected = self.weaviate_manager.connect()
        return self.connected
    
    def disconnect(self):
        """Disconnect from Weaviate"""
        self.weaviate_manager.disconnect()
        self.connected = False
    
    async def search_text(
        self, 
        query: str, 
        limit: int = 5,
        collection_name: str = "Question",
        return_metadata: bool = True
    ) -> List[Dict[str, Any]]:
        """Search using text query
        
        Parameters
        ----------
        query : str
            Text query
        limit : int, optional
            Number of results, by default 5
        collection_name : str, optional
            Collection to search, by default "Question"
        return_metadata : bool, optional
            Whether to return metadata, by default True
            
        Returns
        -------
        List[Dict[str, Any]]
            Search results
        """
        if not self.connected:
            logger.error("Not connected to Weaviate")
            return []
        
        return await self.weaviate_manager.query_near_text(
            query=query,
            limit=limit,
            collection_name=collection_name,
            return_metadata=return_metadata
        )
    
    async def search_vector(
        self, 
        vector: Union[List[float], np.ndarray], 
        limit: int = 5,
        collection_name: str = "Question",
        return_metadata: bool = True
    ) -> List[Dict[str, Any]]:
        """Search using vector query
        
        Parameters
        ----------
        vector : Union[List[float], np.ndarray]
            Query vector
        limit : int, optional
            Number of results, by default 5
        collection_name : str, optional
            Collection to search, by default "Question"
        return_metadata : bool, optional
            Whether to return metadata, by default True
            
        Returns
        -------
        List[Dict[str, Any]]
            Search results
        """
        if not self.connected:
            logger.error("Not connected to Weaviate")
            return []
        
        return await self.weaviate_manager.query_near_vector(
            vector=vector,
            limit=limit,
            collection_name=collection_name,
            return_metadata=return_metadata
        )
    
    def get_collection_info(self, collection_name: str = "Question") -> Dict[str, Any]:
        """Get information about a collection
        
        Parameters
        ----------
        collection_name : str, optional
            Collection name, by default "Question"
            
        Returns
        -------
        Dict[str, Any]
            Collection information
        """
        if not self.connected:
            logger.error("Not connected to Weaviate")
            return {}
        
        return self.weaviate_manager.get_collection_info(collection_name)
    
    def list_collections(self) -> List[str]:
        """List all available collections
        
        Returns
        -------
        List[str]
            List of collection names
        """
        if not self.connected:
            logger.error("Not connected to Weaviate")
            return []
        
        return self.weaviate_manager.list_collections()
    
    def format_results(self, results: List[Dict[str, Any]], show_metadata: bool = True) -> str:
        """Format search results for display
        
        Parameters
        ----------
        results : List[Dict[str, Any]]
            Search results
        show_metadata : bool, optional
            Whether to show metadata, by default True
            
        Returns
        -------
        str
            Formatted results
        """
        if not results:
            return "No results found."
        
        formatted = f"Found {len(results)} result(s):\n\n"
        
        for i, result in enumerate(results, 1):
            formatted += f"Result #{i}:\n"
            
            # Extract main content
            text_content = (
                result.get("text") or 
                result.get("page_content") or 
                result.get("content") or
                "No text content found"
            )
            
            formatted += f"Text: {text_content[:200]}{'...' if len(text_content) > 200 else ''}\n"
            
            # Show source information
            if "source" in result:
                formatted += f"Source: {result['source']}\n"
            
            if "chunk_index" in result:
                formatted += f"Chunk: {result['chunk_index']}\n"
            
            # Show metadata if requested
            if show_metadata and "_metadata" in result:
                metadata = result["_metadata"]
                if "score" in metadata:
                    formatted += f"Score: {metadata['score']:.4f}\n"
                if "distance" in metadata:
                    formatted += f"Distance: {metadata['distance']:.4f}\n"
            
            formatted += "\n" + "-" * 50 + "\n\n"
        
        return formatted


async def main():
    """Example usage of the Weaviate query interface"""
    
    # Initialize query interface
    query_interface = WeaviateQueryInterface()
    
    # Connect to Weaviate
    if not query_interface.connect():
        print("Failed to connect to Weaviate")
        return
    
    print("Connected to Weaviate successfully!")
    
    try:
        # List available collections
        collections = query_interface.list_collections()
        print(f"Available collections: {collections}")
        
        # Check if we have any collections
        if not collections:
            print("No collections found. Please run the index builder first.")
            return
        
        # Use the first available collection
        collection_name = collections[0]
        print(f"Using collection: {collection_name}")
        
        # Get collection info
        info = query_interface.get_collection_info(collection_name)
        print(f"Collection info: {json.dumps(info, indent=2)}")
        
        # Example text queries
        test_queries = [
            "biology",
            "machine learning",
            "artificial intelligence",
            "data science"
        ]
        
        for query in test_queries:
            print(f"\n{'='*60}")
            print(f"Searching for: '{query}'")
            print('='*60)
            
            # Perform text search
            results = await query_interface.search_text(
                query=query,
                limit=3,
                collection_name=collection_name
            )
            
            # Format and display results
            formatted_results = query_interface.format_results(results)
            print(formatted_results)
    
    except Exception as e:
        print(f"Error during querying: {e}")
        logger.error(f"Query error: {e}")
    
    finally:
        # Disconnect
        query_interface.disconnect()
        print("Disconnected from Weaviate")


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s"
    )
    
    # Run the example
    asyncio.run(main())
