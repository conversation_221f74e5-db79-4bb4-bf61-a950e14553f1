import sys
import torch
import faiss
import pickle
import logging
import numpy as np
from typing import Optional
from functools import lru_cache
from enum import StrEnum
from src.globalvariables import (
    VECTOR_STORE_PATH,
    IndexType,
    EMBEDDING_NAME,
)
from src.embeddingloader import EmbeddingModelLoader
from langchain_community.vectorstores import Chroma
from src.chunker import cache_chunker_embedding_chain, BM25Retriever
from langchain_community.embeddings import SentenceTransformerEmbeddings
from utils import collect_metadata_stats

logging.basicConfig(
    stream=sys.stdout,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)


# --- Metadata storage mode ---------------------------------------------------
class MetadataStorageMode(StrEnum):
    """Controls how document metadata is stored with chunks.

    INPLACE    -> full metadata duplicated on every chunk (legacy behaviour)
    REFERENCE  -> only ``doc_id`` stored on each chunk, full metadata kept in
                  a separate pickle keyed by ``doc_id``.
    """

    INPLACE = "inplace"
    REFERENCE = "reference"

# set mode to REFERENCE by default
METADATA_STORAGE_MODE: MetadataStorageMode = MetadataStorageMode.REFERENCE  


@lru_cache(maxsize=None)
@cache_chunker_embedding_chain
class EmbeddingVectors:
    def __init__(
        self,
        tokenizer,
        model,
        create_new_vs,
        existing_vector_store,
        new_vs_name,
        embedding_model_name=EMBEDDING_NAME,
        embedding_type="faiss",
    ):
        """
        Creating embedding vector for different vector class

        Parameters
        ----------
            tokenizer (tokenizer model): tokenizer model)
            model (huggingface model) :  The model of choice. loading is usually from HuggingFace.
            create_new_vs (str): flag to create a new index
            existing_vector_store (str): flag to indicate existing vector store
            new_vs_name (str): New vector store name. name are separated by underscore (_). e.x This_is_a_new_vector_store_name
            embedding_model_name (embedding model), optional : name of the embedding model used for HuggingFaceInstructEmbeddings. The default is "sentence-transformers/all-mpnet-base-v2".
            embedding_type (str), optional : Type of embedding type e.g faiss or chroma or weaviate. The default is "faiss".

        Raises
        ------
            ValueError : Returns ValueError in case of unsupported vector name.

        Returns
        ------
            None.

        """
        self.tokenizer = tokenizer
        self.model = model
        self.embedding_type = embedding_type
        self.create_new_vs = create_new_vs
        self.existing_vector_store = existing_vector_store
        self.new_vs_name = new_vs_name
        self.device = torch.device(
            "cuda"
            if torch.cuda.is_available()
            else "cpu" if torch.backends.mps.is_available() else "cpu"
        )
        self.embedding_model_name = embedding_model_name
        self.embedding_model = EmbeddingModelLoader.load_embedding_model(
            self.embedding_type, embedding_model_name
        )
        # --
        self.embedding_dimension = (
            EmbeddingModelLoader.get_embedding_dimension(
                self.embedding_type, embedding_model_name
            )
        )
        if self.embedding_type == IndexType.WEAVIATE:
            self.class_name = "Document"

    @classmethod
    async def create_async(
        cls,
        tokenizer,
        model,
        create_new_vs,
        existing_vector_store,
        new_vs_name,
        embedding_model_name=EMBEDDING_NAME,
        embedding_type="faiss",
    ):
        """
        Async factory method that creates an instance and initializes it asynchronously
        """
        instance = cls(
            tokenizer,
            model,
            create_new_vs,
            existing_vector_store,
            new_vs_name,
            embedding_model_name,
            embedding_type,
        )

        # Then update the embedding model asynchronously
        instance.embedding_model = (
            await EmbeddingModelLoader.load_embedding_model_async(
                embedding_type, embedding_model_name
            )
        )
        return instance

    def _split_chunks(self, chunks):
        """Utility: split a list of chunks that might be (text, meta) tuples."""
        if not chunks:
            return [], []
        if isinstance(chunks[0], tuple) and len(chunks[0]) == 2:
            texts, metas = zip(*chunks)
            return list(texts), list(metas)
        return chunks, []

    # ------------------------------------------------------------------
    # Metadata helpers
    # ------------------------------------------------------------------
    @staticmethod
    def _prepare_metadata_for_saving(metadatas: list[dict]):
        """Split full chunk metadata list into

        1. *chunk_metadatas* – a lightweight list aligned with *texts* that
           contains only ``doc_id`` (and optional ``chunk_id``).
        2. *doc_metadata_collection* – dict mapping ``doc_id`` → full metadata

        Parameters
        ----------
        metadatas : list[dict]
            Original metadata list (one entry per chunk).

        Returns
        -------
        tuple[list[dict], dict]
            ``(chunk_metadatas, doc_metadata_collection)``
        """
        doc_metadata_collection: dict = {}
        chunk_metadatas: list[dict] = []

        for meta in metadatas:
            if not isinstance(meta, dict):  # Defensive – copy as-is
                chunk_metadatas.append(meta or {})
                continue

            doc_id = meta.get("doc_id")
            if doc_id is None:
                # No doc_id → leave metadata untouched (legacy behaviour)
                chunk_metadatas.append(meta)
                continue

            # Ensure single entry per document in the collection
            if doc_id not in doc_metadata_collection:
                # Exclude chunk-level keys from doc-level store
                doc_meta = {k: v for k, v in meta.items() if k not in {"chunk_id"}}
                doc_metadata_collection[doc_id] = doc_meta

            # Simplified metadata for the chunk (only ids)
            simplified = {"doc_id": doc_id}
            if "chunk_id" in meta:
                simplified["chunk_id"] = meta["chunk_id"]
            chunk_metadatas.append(simplified)

        return chunk_metadatas, doc_metadata_collection

    def create_embeddings(self, texts, batch_size: Optional[int] = None):
        """
        Create_embeddings.
        Creates embeddings using pre-loaded SentenceTransformer or tokenizer based on availability.

        Parameters:
            texts (str): input texts

        Returns:
            np.array: The embedding vectors
        """
        self.batch_size = 32 if not batch_size else batch_size
        try:
            if not texts or len(texts) == 0:
                logging.error("🚩 Empty texts array received")
                return np.zeros((0, self.embedding_dimension))

            if self.embedding_type in [IndexType.FAISS, IndexType.CHROMA]:
                logging.info(
                    f"Creating embeddings on device: {self.device.type}"
                )

                all_embeddings = []

                for i in range(0, len(texts), self.batch_size):
                    batch_texts = texts[i : i + self.batch_size]

                    embeddings = self.embedding_model.encode(
                        batch_texts,
                        show_progress_bar=(
                            True if len(batch_texts) > 10 else False
                        ),
                        convert_to_tensor=True,
                        device=self.device.type,
                    )
                    batch_embeddings = (
                        embeddings.to(dtype=torch.float32).cpu().numpy()
                    )
                    all_embeddings.append(batch_embeddings)

                # Combine batches
                if all_embeddings:
                    combined_embeddings = np.vstack(all_embeddings)
                    # -- verify the embedding dimension
                    if (
                        combined_embeddings.shape[1]
                        != self.embedding_dimension
                    ):
                        logging.warning(
                            f"Embedding dimension mismatch! Expected {self.embedding_dimension}, got {combined_embeddings.shape[1]}"
                        )
                        self.embedding_dimension = combined_embeddings.shape[1]
                        cache_key = f"{self.embedding_type}_{self.embedding_model_name}"
                        EmbeddingModelLoader._dimension_cache[cache_key] = (
                            self.embedding_dimension
                        )
                    return combined_embeddings
                return np.zeros((0, self.embedding_dimension))

            elif self.embedding_type == IndexType.WEAVIATE:
                # Use tokenizer-based embeddings for Weaviate
                try:
                    # -- process embedding in batches
                    all_embeddings = []

                    for i in range(0, len(texts), self.batch_size):
                        batch_texts = texts[i : i + self.batch_size]

                        inputs = self.tokenizer(
                            batch_texts,
                            return_tensors="pt",
                            padding=True,
                            truncation=True,
                            max_length=self.tokenizer.model_max_length,
                        ).to(self.device.type)

                        if (
                            inputs["input_ids"].size(1)
                            > self.tokenizer.model_max_length
                        ):
                            logging.warning(
                                "🚩 Input text exceeds model's maximum length, truncating."
                            )

                        with torch.no_grad():
                            embeddings = self.model.transformer.wte(
                                inputs["input_ids"]
                            ).mean(dim=1)
                        batch_embeddings = (
                            embeddings.to(dtype=torch.float32).cpu().numpy()
                        )
                        all_embeddings.append(batch_embeddings)

                    # Combine batches
                    if all_embeddings:
                        combined_embeddings = np.vstack(all_embeddings)
                        if (
                            combined_embeddings.shape[1]
                            != self.embedding_dimension
                        ):
                            logging.warning(
                                f" Embedding dimension mismatch! Expected {self.embedding_dimension}, got {combined_embeddings.shape[1]}"
                            )
                            self.embedding_dimension = (
                                combined_embeddings.shape[1]
                            )
                        return combined_embeddings
                    return np.zeros((0, self.embedding_dimension))

                except IndexError as e:
                    logging.error(
                        f"🚩 Index out of range error: {e}. Check input text length."
                    )
                    return np.zeros((0, self.embedding_dimension))
            else:
                logging.error(
                    f"🚩 Unsupported embedding type: {self.embedding_type}"
                )
                return np.zeros((0, self.embedding_dimension))

        except Exception as e:
            logging.error(f"🚩 Error creating embeddings: {e}")
            return np.zeros((0, self.embedding_dimension))

    def create_faiss_index(self, embeddings, chunk_size=None):
        """
        Create FAISS index with dimension validation and error handling

        Parameters:
            embeddings (np.array): text embeddings
            chunk_size (int): chunk size to split texts

        Returns
            Index/Vector store
        """
        try:
            if embeddings is None or len(embeddings) == 0:
                logging.error("🚩 Empty embeddings array received")
                return None

            assert isinstance(
                embeddings, np.ndarray
            ), f"Embedding is type : {type(embeddings)} not an ndarray"

            if embeddings.shape[0] == 0 or embeddings.shape[1] == 0:
                logging.error("🚩 Embeddings array has zero dimensions")
                return None

            if np.isnan(embeddings).any() or np.isinf(embeddings).any():
                logging.error("🚩 Embeddings contain NaN or Inf values")
                return None
            # --
            dimension = embeddings.shape[1]
            logging.info(f"Creating FAISS index with dimension: {dimension}")
            if dimension != self.embedding_dimension:
                logging.warning(
                    f" Updating embedding dimension from {self.embedding_dimension} to {dimension}"
                )
                self.embedding_dimension = dimension
                cache_key = (
                    f"{self.embedding_type}_{self.embedding_model_name}"
                )
                EmbeddingModelLoader._dimension_cache[cache_key] = dimension

            # -- Indexing
            train = self.device.type != "cpu"
            embeddings_copy = embeddings.copy().astype(np.float32)
            faiss.normalize_L2(embeddings_copy)
            index = faiss.IndexFlatL2(dimension)
            index.add(embeddings_copy)

            # -- return index for small dataset
            if embeddings.shape[0] < 1000:
                logging.info(
                    f"Using flat index for small dataset ({embeddings.shape[0]} points)"
                )
                return index

            # otherwise, use GPUs to create IVF index
            if train and embeddings.shape[0] >= 1000:
                try:
                    nlist = min(
                        4096, max(int(np.sqrt(embeddings.shape[0])), 4)
                    )
                    if embeddings.shape[0] < 30 * nlist:
                        logging.warning(
                            f"🚩 Not enough training data for IVF. Using flat index instead. "
                            f"Need at least {30 * nlist} points, but have {embeddings.shape[0]}."
                        )
                        return index

                    logging.info(f"Creating IVF index with {nlist} centroids")
                    # -- Train IVF index
                    quantizer = faiss.IndexFlatL2(dimension)
                    ivf_index = faiss.IndexIVFFlat(
                        quantizer, dimension, nlist, faiss.METRIC_INNER_PRODUCT
                    )
                    ivf_index.train(embeddings_copy)
                    ivf_index.add(embeddings_copy)
                    ivf_index.nprobe = max(1, nlist // 10)

                    if not ivf_index.is_trained:
                        logging.error(
                            "🚩 FAISS IVF index training failed! Falling back to flat index."
                        )
                        return index

                    return ivf_index
                except Exception as e:
                    logging.error(
                        f"🚩 Error creating FAISS IVF index: {e}. Falling back to flat index."
                    )
                    return index

            return index

        except Exception as e:
            logging.error(f"🚩 Error creating FAISS index: {e}")
            return None

    def save_index(self, index, texts, metadatas=None, meta_stats=None, doc_metadata_collection=None):
        """
        Save index -- vector database
        If create_new_vs is True, create a new vector store.
        Otherwise, merge the new vectors with the existing index.

        Parameters:
                index (Index/vector store): Index or vector store
                texts: input texts
                metadatas: list of metadatas corresponding to texts
                meta_stats: metadata statistics for facet inventory

        Returns
            None
        """
        try:
            if self.create_new_vs:
                save_path = (
                    VECTOR_STORE_PATH
                    / f"{self.embedding_type}_{self.new_vs_name}"
                )
            else:
                save_path = (
                    VECTOR_STORE_PATH
                    / f"{self.embedding_type}_{self.existing_vector_store}"
                )

            save_path.mkdir(parents=True, exist_ok=True)

            # -- index dimension info
            dimension_info = {
                "embedding_model_name": self.embedding_model_name,
                "embedding_dimension": self.embedding_dimension,
            }
            with open(save_path / "dimension_info.pkl", "wb") as f:
                pickle.dump(dimension_info, f)

            # Persist the document-level metadata collection if reference mode is active
            if doc_metadata_collection:
                doc_meta_path = save_path / "doc_meta_collection.pkl"
                try:
                    if doc_meta_path.exists():
                        with open(doc_meta_path, "rb") as f:
                            existing_doc_metas = pickle.load(f)
                    else:
                        existing_doc_metas = {}

                    # Merge and persist
                    existing_doc_metas.update(doc_metadata_collection)
                    with open(doc_meta_path, "wb") as f:
                        pickle.dump(existing_doc_metas, f)
                except Exception as exc:
                    logging.error("🚩 Error saving doc_meta_collection: %s", exc)

            # ensure metadatas aligns with texts length
            if metadatas is None:
                metadatas = [{} for _ in texts]

            # Persist metadata statistics (facet inventory) if supplied
            if meta_stats is not None:
                try:
                    with open(save_path / "meta_stats.pkl", "wb") as f:
                        pickle.dump(meta_stats, f)
                except Exception as e:
                    logging.error(f"🚩 Error saving meta_stats: {e}")

            # -- initialize and save BM25 retriever
            try:
                bm25_retriever = BM25Retriever(texts)
                bm25_retriever.save_bm25(save_path / "bm25_retriever.pkl")
            except Exception as e:
                logging.error(f"🚩 Error saving BM25 retriever: {e}")

            if self.embedding_type == IndexType.FAISS:
                if index is None:
                    logging.error("🚩 Cannot save None FAISS index")
                    return

                if self.create_new_vs:
                    faiss.write_index(index, str(save_path / "faiss.index"))
                    with open(save_path / "faiss.pkl", "wb") as f:
                        pickle.dump(texts, f)
                    with open(save_path / "faiss_meta.pkl", "wb") as f:
                        pickle.dump(metadatas, f)
                    logging.info(
                        f"New FAISS index and texts saved successfully to {save_path}"
                    )
                else:
                    try:
                        try:
                            with open(
                                save_path / "dimension_info.pkl", "rb"
                            ) as f:
                                existing_dimension_info = pickle.load(f)
                                existing_dimension = (
                                    existing_dimension_info.get(
                                        "embedding_dimension"
                                    )
                                )

                                if (
                                    existing_dimension
                                    != self.embedding_dimension
                                ):
                                    logging.error(
                                        f"🚩 Dimension mismatch! Existing index has dimension {existing_dimension}, "
                                        f"but current embeddings have dimension {self.embedding_dimension}. "
                                        f"Cannot merge indices with different dimensions."
                                    )
                                    return
                        except FileNotFoundError:
                            logging.warning(
                                "No dimension info found for existing index. Proceeding with caution."
                            )

                        existing_index = faiss.read_index(
                            str(save_path / "faiss.index")
                        )

                        # -- checking dimension mismatch
                        if (
                            hasattr(existing_index, "d")
                            and existing_index.d != self.embedding_dimension
                        ):
                            logging.error(
                                f"🚩 Dimension mismatch! Existing index has dimension {existing_index.d}, "
                                f"but current embeddings have dimension {self.embedding_dimension}. "
                                f"Cannot merge indices with different dimensions."
                            )
                            return

                        # -- merging index if possible
                        if hasattr(existing_index, "merge_from"):
                            existing_index.merge_from(index)
                        else:
                            logging.warning(
                                "🚩 Index doesn't support merge_from. Creating a new index."
                            )
                            with open(save_path / "faiss.pkl", "rb") as f:
                                existing_texts = pickle.load(f)
                            with open(save_path / "faiss_meta.pkl", "rb") as f:
                                existing_metas = pickle.load(f)

                            existing_embeddings = self.create_embeddings(
                                existing_texts
                            )
                            new_embeddings = self.create_embeddings(texts)
                            combined_embeddings = np.vstack(
                                [existing_embeddings, new_embeddings]
                            )
                            combined_texts = existing_texts + texts
                            combined_index = faiss.IndexFlatL2(
                                self.embedding_dimension
                            )
                            combined_embeddings_copy = (
                                combined_embeddings.copy().astype(np.float32)
                            )
                            faiss.normalize_L2(combined_embeddings_copy)
                            combined_index.add(combined_embeddings_copy)
                            faiss.write_index(
                                combined_index, str(save_path / "faiss.index")
                            )
                            with open(save_path / "faiss.pkl", "wb") as f:
                                pickle.dump(combined_texts, f)
                            with open(save_path / "faiss_meta.pkl", "wb") as f:
                                pickle.dump(existing_metas + metadatas, f)
                            logging.info(
                                f"Created new combined FAISS index and saved to {save_path}"
                            )
                            return

                        # -- write and merge index
                        faiss.write_index(
                            existing_index, str(save_path / "faiss.index")
                        )
                        with open(save_path / "faiss.pkl", "rb") as f:
                            existing_texts = pickle.load(f)
                        with open(save_path / "faiss_meta.pkl", "rb") as f:
                            existing_metas = pickle.load(f)
                        existing_texts.extend(texts)
                        with open(save_path / "faiss.pkl", "wb") as f:
                            pickle.dump(existing_texts, f)
                        with open(save_path / "faiss_meta.pkl", "wb") as f:
                            pickle.dump(existing_metas + metadatas, f)
                        logging.info(
                            f"FAISS index merged and texts updated successfully at {save_path}"
                        )

                    except FileNotFoundError:
                        logging.warning(
                            f"🚩 Existing index not found at {save_path}. Creating new index."
                        )
                        faiss.write_index(
                            index, str(save_path / "faiss.index")
                        )
                        with open(save_path / "faiss.pkl", "wb") as f:
                            pickle.dump(texts, f)
                        with open(save_path / "faiss_meta.pkl", "wb") as f:
                            pickle.dump(metadatas, f)
                        logging.info(
                            f"New FAISS index created and saved to {save_path}"
                        )

            elif self.embedding_type == IndexType.CHROMA:
                try:
                    if self.create_new_vs:
                        # -- wrap SentenceTransformer with LangChain embedding interface
                        #   SentenceTransformerEmbeddings expects the *name* of a HF model, not the model instance.
                        #   We therefore pass the configured model name and let LangChain handle the loading.
                        embedding_fn = SentenceTransformerEmbeddings(model_name=self.embedding_model_name)

                        # Explicitly set the Chroma collection name to align with `new_vs_name` so that
                        # downstream callers (e.g. tests) can reliably fetch the collection.
                        vectorstore = Chroma.from_texts(
                            texts,
                            embedding=embedding_fn,
                            persist_directory=str(save_path),
                            metadatas=metadatas,
                            collection_name=self.new_vs_name,
                        )
                        vectorstore.persist()
                        logging.info(
                            f"New Chroma index and texts saved successfully to {save_path}"
                        )
                    else:
                        # Opening an existing Chroma collection requires the same explicit collection name.
                        existing_vectorstore = Chroma(
                            embedding_function=SentenceTransformerEmbeddings(model_name=self.embedding_model_name),
                            persist_directory=str(save_path),
                            collection_name=self.existing_vector_store or self.new_vs_name,
                        )
                        existing_vectorstore.add_texts(texts, metadatas=metadatas)
                        existing_vectorstore.persist()
                        logging.info(
                            f"Chroma index updated with new texts at {save_path}"
                        )
                except Exception as e:
                    logging.error(f"🚩 Error with Chroma vectorstore: {e}")
                    raise

            elif self.embedding_type == IndexType.WEAVIATE:
                try:
                    if self.create_new_vs:
                        for i, text in enumerate(texts):
                            self.embedding_model.batch.add_data_object(
                                {"text": text},
                                self.class_name,
                                vector=self.embeddings[i],
                            )
                        self.embedding_model.batch.flush()
                        logging.info(
                            "New Weaviate index created and texts saved successfully"
                        )
                    else:
                        for i, text in enumerate(texts):
                            self.embedding_model.batch.add_data_object(
                                {"text": text},
                                self.class_name,
                                vector=self.embeddings[i],
                            )
                        self.embedding_model.batch.flush()
                        logging.info("Weaviate index updated with new texts")
                except Exception as e:
                    logging.error(f"🚩 Error with Weaviate: {e}")
                    raise

            else:
                raise ValueError(
                    "🚩 Unsupported embedding type. Choose 'faiss', 'chroma', or 'weaviate'."
                )

        except Exception as e:
            logging.error(
                f"🚩 An error occurred while saving/merging the index: {str(e)}"
            )
            raise

    def create_and_save_index(self, chunks, enable_meta_filter=True):
        """
        Create and save the index -- vector DB with improved validation and error handling

        Parameters:
            chunks: list of text chunks
            enable_meta_filter: bool, whether to enable metadata filtering (default: True)

        Return
            None
        """
        try:
            # split chunks into texts & metadatas
            texts, metadatas = self._split_chunks(chunks)

            if not texts:
                logging.error("🚩 Empty texts array received")
                return

            self.embeddings = self.create_embeddings(texts)
            if self.embeddings is None or len(self.embeddings) == 0:
                logging.error("🚩 Failed to create embeddings")
                return


            # Collect facet inventory only when enabled
            meta_stats = None
            if enable_meta_filter:
                meta_stats = collect_metadata_stats(metadatas)

            if self.embedding_type == IndexType.FAISS:
                index = self.create_faiss_index(self.embeddings)
                if index is not None:
                    # Decide how to store metadata based on global flag
                    if METADATA_STORAGE_MODE == MetadataStorageMode.REFERENCE:
                        chunk_metas, doc_meta_coll = self._prepare_metadata_for_saving(metadatas)
                        self.save_index(
                            index,
                            texts,
                            chunk_metas,
                            meta_stats,
                            doc_metadata_collection=doc_meta_coll,
                        )
                    else:  # Legacy *inplace* behaviour
                        self.save_index(
                            index,
                            texts,
                            metadatas,
                            meta_stats,
                        )
                else:
                    logging.error("🚩 Failed to create FAISS index")
            elif self.embedding_type == IndexType.CHROMA:
                self.save_index(None, texts, metadatas, meta_stats)
            elif self.embedding_type == IndexType.WEAVIATE:
                self.save_index(None, texts, metadatas, meta_stats)
            else:
                raise ValueError(
                    "🚩 Unsupported embedding type. Choose 'faiss', 'chroma', or 'weaviate'."
                )
        except Exception as e:
            logging.error(f"🚩 Error in create_and_save_index: {e}")
