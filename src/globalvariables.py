import os
from enum import Enum, StrEnum
from pathlib import Path

from configuration import get_backend_config

# %% Directory

REPO_PATH = Path(__file__).parent.parent
DATA_PATH = REPO_PATH / "data"
IMG_PATH = REPO_PATH / "image"
VECTOR_STORE_PATH = REPO_PATH / "vector_store"
EMBEDDING_CACHE_STORE = ".cache/"

# -- Check if paths exist otherwise, create one
if not os.path.exists(DATA_PATH):
    os.makedirs(DATA_PATH)

if not os.path.exists(IMG_PATH):
    os.makedirs(IMG_PATH)

if not os.path.exists(VECTOR_STORE_PATH):
    os.makedirs(VECTOR_STORE_PATH)
# %%
# -- Helper for suggestions
HELP = {  # help suggestions..
    "HuggingFace": "You can get theHuggingFace token from settings of your Huggingface account",
    "LLM_Model": "An instruction LLM model well (distilled or not) necessary to provide the right anwser"
    + "\n"
    "toward the particular context",
    "Instruction_Embedding": "An instruction LLM Embedding well suited to provide the right anwser"
    + "\n"
    + "toward the particular context",
    "Vector_store": "A lsit vector embedding created using the instruction embedding",
    "Temperature": "Apply a larger temperature when sampling for challenging tokens, allowing LLMs to explore"
    + "\n"
    + "diverse choices. A smaller temperature for confident tokens avoiding the influence "
    + "\n"
    + "of tail randomness noises",
    "Max_characer": "The maximum number of characters to generated. This can be similar to the maximum token"
    + "\n"
    + " size of the embedding space. The default is set to 500.",
    "index_type": "Select desired vector types",
    "pipeline": "Select the desired pipeline. Default is without Chain of Thought (COT)",
    "template": "Select a template style of choice. Default is a simple template.",
    "reranker": "Reranker algorithm selects between two different response types. The first is Reciprocal Rank Fusion,"
    + "\n"
    + "The other is the Flash reranker, which uses a Cross-Encoder for reranking.",
    "new_vector_store": "If choose <New> in the dropdown / multiselect box, name the new vector store. Otherwise, "
    + "\n"
    + "fill in the existing vector store to merge.",
}

# -- Template for LLM prompting -->
TEMPLATE = ["Default", "Custom"]

# -- Summplementary models for Embedding
SUPPLEMENT = [
    "Alibaba-NLP/gte-large-en-v1.5",
    "sentence-transformers/all-mpnet-base-v2",
    "mixedbread-ai/mxbai-embed-large-v1",
    "WhereIsAI/UAE-Large-V1",
    "avsolatorio/GIST-large-Embedding-v0",
    "w601sxs/b1ade-embed",
    "Labib11/MUG-B-1.6",
    "WhereIsAI/UAE-Large-V1",
]

# -- Embedding name
EMBEDDING_NAME = "sentence-transformers/all-mpnet-base-v2"  # all-mpnet-base-v2 is best;  "sentence-transformers/all-MiniLM-L6-v2" (competitive)

SINGLE_FILE = 1  # single files
RANDOM_SEED = 42
MAX_MODEL_LEN: int = (
    None  # <-- switch maximum model length here. 64 -> Llama3-70; 128 -> Llama3-8b
)

# %% reasoning types


class ReasoningType(Enum):
    """Defines different types of reasoning for the LLM"""

    FACTUAL = "factual"
    ANALYTICAL = "analytical"
    COMPARATIVE = "comparative"
    CAUSAL = "causal"
    HYPOTHETICAL = "hypothetical"


# %% StrEnums


class Reranker(StrEnum):
    RRF = "RRF"
    FLASHRERANKER = "FlashReranker"


class Models(StrEnum):
    LLAMA31_8B = "neuralmagic/Meta-Llama-3.1-8B-Instruct-quantized.w4a16"
    LLAMA31_70B = "neuralmagic/Meta-Llama-3.1-70B-Instruct-quantized.w4a16"
    LLAMA31_405B = "neuralmagic/Meta-Llama-3.1-405B-Instruct-quantized.w4a16"
    LLAMA3_8B = "neuralmagic/Meta-Llama-3-8B-Instruct-quantized.w4a16"
    LLAMA3_70B = "neuralmagic/Meta-Llama-3-70B-Instruct-quantized.w4a16"
    LLAMA2_7B = "neuralmagic/Llama-2-7b-chat-quantized.w4a16"
    LLAMA2_13B = "TheBloke/Llama-2-13B-chat-GPTQ"
    MISTRAL_LARGE = "neuralmagic/Mistral-7B-Instruct-v0.3-quantized.w4a16"
    MISTRAL_SMALL = "neuralmagic/Mistral-Nemo-Instruct-2407-quantized.w4a16"
    GEMMA2 = "neuralmagic/gemma-2-9b-it-quantized.w4a16"
    TINYLLAMA = "neuralmagic/TinyLlama-1.1B-Chat-v1.0-marlin"
    LAMINIGPT = "MBZUAI/LaMini-GPT-774M"
    LAMININEO = "MBZUAI/LaMini-Neo-125M"
    LAMINICEREBRAS = "MBZUAI/LaMini-Cerebras-590M"
    LAMNINIFLAN = "MBZUAI/LaMini-Flan-T5-783M"


class GPUModels(StrEnum):
    LLAMA31_8B = "neuralmagic/Meta-Llama-3.1-8B-Instruct-quantized.w4a16"
    LLAMA31_70B = "neuralmagic/Meta-Llama-3.1-70B-Instruct-quantized.w4a16"
    LLAMA31_405B = "neuralmagic/Meta-Llama-3.1-405B-Instruct-quantized.w4a16"
    LLAMA3_8B = "neuralmagic/Meta-Llama-3-8B-Instruct-quantized.w4a16"
    LLAMA3_70B = "neuralmagic/Meta-Llama-3-70B-Instruct-quantized.w4a16"
    LLAMA2_7B = "neuralmagic/Llama-2-7b-chat-quantized.w4a16"
    LLAMA2_13B = "TheBloke/Llama-2-13B-chat-GPTQ"
    MISTRAL_LARGE = "neuralmagic/Mistral-7B-Instruct-v0.3-quantized.w4a16"
    MISTRAL_SMALL = "neuralmagic/Mistral-Nemo-Instruct-2407-quantized.w4a16"
    GEMMA2 = "neuralmagic/gemma-2-9b-it-quantized.w4a16"
    TINYLLAMA = "neuralmagic/TinyLlama-1.1B-Chat-v1.0-marlin"


class CPUModels(StrEnum):
    LLAMA32_3B_INSTRUCT = "patrickzj/Llama-3.2-3B-Instruct-Q2_K-GGUF"


class CPUTokenizer(StrEnum):
    LLAMA32_3B_INSTRUCT_TOK = (
        "fbaldassarri/meta-llama_Llama-3.2-3B-Instruct-auto_awq-int4-gs128-sym"
    )


GPU_MODEL_SET = {model for model in GPUModels}
CPU_MODEL_SET = {model for model in CPUModels}

DEFAULT_CPU_MODEL = CPUModels.LLAMA32_3B_INSTRUCT
DEFAULT_CPU_TOKENIZER = CPUTokenizer.LLAMA32_3B_INSTRUCT_TOK
LARGE_MODELS = [
    Models.LLAMA31_8B,
    Models.LLAMA31_70B,
    Models.LLAMA31_405B,
    Models.LLAMA3_70B,
    Models.MISTRAL_LARGE,
]


class ChunkingMethod(StrEnum):
    RECURSIVE_CHARACTER = "recursive_character"
    FIXED = "fixed"
    SEMANTIC = "semantic"
    TOKEN_BASED = "token_based"
    HIERARCHICAL = "hierarchical"
    MODEL_BASED = "model_based"


class IndexType(StrEnum):
    FAISS = "faiss"
    CHROMA = "chroma"
    WEAVIATE = "weaviate"


class OptimalMethod(StrEnum):
    ELBOW = "elbow"
    SILHOUETTE = "silhouette"
    GAP = "gap"


class PipelineType(StrEnum):
    HAHCOMPOSITE = "C-HAH RAG"
    HAH = "HAH RAG"
    NAIVE = "Naive RAG"


class OCRConfig(Enum):
    USE_OCR = not get_backend_config().disable_ocr_for_pdf
    OCR_DPI = 150
    FORCE_OCR = get_backend_config().force_ocr_on_all_pdf
    LANGS = os.getenv("LANGS", "eng fra deu spa ita por kor ara").split(" ")


class GPUMemoryStatus(Enum):
    AVAILABLE = "available"
    INSUFFICIENT = "insufficient"
    UNAVAILABLE = "unavailable"


# -- GPU memory requirements (in GiB)
GPU_MEMORY_REQUIREMENTS = {
    "neuralmagic/Meta-Llama-3.1-8B-Instruct-quantized.w4a16": 16,
    "neuralmagic/Meta-Llama-3.1-70B-Instruct-quantized.w4a16": 70,
    "neuralmagic/Meta-Llama-3.1-405B-Instruct-quantized.w4a16": 200,
    "neuralmagic/Meta-Llama-3-8B-Instruct-quantized.w4a16": 16,
    "neuralmagic/Meta-Llama-3-70B-Instruct-quantized.w4a16": 70,
    "neuralmagic/Llama-2-7b-chat-quantized.w4a16": 14,
    "TheBloke/Llama-2-13B-chat-GPTQ": 26,
    "neuralmagic/Mistral-7B-Instruct-v0.3-quantized.w4a16": 14,
    "neuralmagic/Mistral-Nemo-Instruct-2407-quantized.w4a16": 8,
    "neuralmagic/gemma-2-9b-it-quantized.w4a16": 18,
    "neuralmagic/TinyLlama-1.1B-Chat-v1.0-marlin": 2,
    "MBZUAI/LaMini-GPT-774M": 8,
    "MBZUAI/LaMini-Neo-125M": 4,
    "MBZUAI/LaMini-Cerebras-590M": 8,
    "MBZUAI/LaMini-Flan-T5-783M": 8,
}

# -- Default requirement (7/8 of H100 memory ~70 GiB)
DEFAULT_GPU_MEMORY_REQUIREMENT = 70

# Safety margin memory (reserved memory, also in GiB)
GPU_MEMORY_SAFETY_MARGIN = 2


# -----------------------------
# Trivial-input detection vocabulary
# -----------------------------


TRIVIAL_LEN: int = 100

TRIVIAL_ENGLISH_VOCABULARY: set[str] = {
    "hello",
    "hi",
    "hey",
    "yo",
    "sup",
    "good morning",
    "good afternoon",
    "good evening",
    "howdy",
    "greetings",
    "how are you",
    "how are ya",
    "thanks",
    "thank you",
    "thx",
    "ty",
    "merci",
    "gracias",
    "cool, thanks",
    "ok, thanks",
    "great, thanks",
    "morning",
    "evening",
    "good noon",
    "good eve",
    "gm",
    "gn",
    "thank u",
    "thanx",
    "thnx",
    "thanks a lot",
    "thank you so much",
    "appreciate it",
    "much obliged",
    "cheers",
    "cheers mate",
    "ta",
    "hiya",
    "wassup",
    "thanks a ton",
    "how are you doing today",
    "good night",
    "night",
    "nite",
    "sleep well",
    "sweet dreams",
    "see ya",
    "see you",
    "bye",
    "goodbye",
    "farewell",
    "catch ya later",
    "later",
    "peace",
    "peace out",
    "take care",
    "have a good one",
    "until next time",
    "ttyl",
    "brb",
    "is",
    "it",
    "you",
    "your",
    "you're",
    "you've",
    "you'll",
    "you'd",
    "in",
    "at",
    "to",
    "of",
    "and",
    "or",
    "but",
    "if",
    "be right back",
    "one sec",
    "hold on",
    "wait up",
    "just a minute",
    "give me a sec",
    "hang tight",
    "bear with me",
    "sorry",
    "my bad",
    "oops",
    "whoops",
    "my apologies",
    "excuse me",
    "pardon",
    "forgive me",
    "apologies",
    "no worries",
    "no problem",
    "dont mention it",
    "you're welcome",
    "anytime",
    "my pleasure",
    "glad to help",
    "happy to help",
    "sure thing",
    "of course",
    "absolutely",
    "definitely",
    "for sure",
    "yep",
    "yeah",
    "yes",
    "yup",
    "uh huh",
    "right on",
    "sounds good",
    "okay",
    "ok",
    "alright",
    "fine",
    "not bad",
    "decent",
    "fair enough",
    "i see",
    "got it",
    "understood",
    "makes sense",
    "right",
    "bingo",
    "that's it",
    "you got it",
    "spot on",
    "on point",
    "nailed it",
    "well done",
    "good job",
    "nice work",
    "keep it up",
    "way to go",
    "congrats",
    "congratulations",
    "well played",
    "impressive",
    "goodnight",
    "adios",
    "ciao",
    "au revoir",
    "sayonara",
    "cheerio",
    "toodles",
    "so long",
    "talk soon",
    "solid",
    "tight",
    "clean",
    "smooth",
    "slick",
    "fresh",
    "crisp",
    "sharp",
    "on fleek",
    "tell me about it",
    "you said it",
    "couldnt agree more",
    "totally",
    "completely",
    "entirely",
    "wholly",
    "utterly",
    "fully",
    "100%",
    "all the way",
    "through and through",
    "to the core",
    "without a doubt",
    "no question",
    "hands down",
    "by far",
    "thats for sure",
    "you bet",
    "you betcha",
    "quite so",
    "quite",
    "somewhat",
    "kind of",
    "sort of",
    "more or less",
    "roughly",
    "approximately",
    "about",
    "around",
    "nearly",
    "almost",
    "close to",
    "just about",
    "not bad at all",
    "never",
    "not ever",
    "at no time",
    "under no circumstances",
    "by no means",
    "not at all",
    "not in the least",
    "not one bit",
    "not a chance",
    "no way",
    "forget it",
    "dream on",
    "in your dreams",
    "fat chance",
    "when pigs fly",
    "over my dead body",
    "not if i can help it",
    "not on my watch",
    "not happening",
    "aint gonna happen",
    "nope",
    "nah",
    "negative",
    "nada",
    "zilch",
    "nothing",
    "none",
    "neither",
    "nor",
    "however",
    "nevertheless",
    "nonetheless",
    "still",
    "yet",
    "though",
    "although",
    "even though",
    "despite",
    "in spite of",
    "regardless",
    "anyway",
    "anyhow",
    "in any case",
    "at any rate",
    "either way",
    "one way or another",
    "somehow",
    "someway",
    "whatever",
    "whenever",
    "wherever",
    "whoever",
    "whomever",
    "whichever",
    "why not",
    "sure why not",
    "why not indeed",
    "indeed why not",
    "what the heck",
    "what the hell",
    "why the hell not",
    "might as well",
    "could be worse",
    "better than nothing",
    "something is better than nothing",
    "half a loaf is better than none",
    "beggars cant be choosers",
    "take what you can get",
    "it is what it is",
    "such is life",
    "thats life",
    "life goes on",
    "cest la vie",
    "what can you do",
    "what are you gonna do",
    "whatcha gonna do",
    "whaddya gonna do",
    "what else is new",
    "same old same old",
    "nothing new under the sun",
    "been there done that",
    "story of my life",
    "tell me something i dont know",
    "no kidding",
    "you dont say",
    "are you serious",
    "are you kidding me",
    "youve got to be kidding",
    "you must be joking",
    "pull the other one",
    "get out of here",
    "get outta here",
    "no way jose",
    "come on",
    "come off it",
    "give me a break",
    "cut it out",
    "knock it off",
    "stop it",
    "quit it",
    "enough",
    "thats enough",
    "im done",
    "im out",
    "i gotta go",
    "anyone",
    "someone",
    "somebody",
    "i",
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
    "I'm",
    "I've",
    "I'll",
    "I'd",
    "re",
    "im",
    "ive",
    "ill",
    "id",
    "youre",
    "youve",
    "gotta run",
    "thank you very much",
    "hey there how are you doing today",
    "hello there good sir hope everything is okay",
    "thank you very much that was very helpful",
    "don",
    "dont",
    "didnt",
    "couldn",
    "couldnt",
    "shouldnt",
    "wont",
    "for",
    "from",
    "as",
    "by",
    "that",
    "this",
    "these",
    "those",
    "them",
    "their",
    "theirs",
    "not",
    "no",
    "got",
    "be",
    "me",
    "out",
    "outta",
    "outta here",
    "outta there",
    "our",
    "ours",
    "yours",
    "anybody",
    "anybody there",
    "anybody there?",
    "my",
    "anywhere",
    "somewhere",
    "everywhere",
    "have",
    "has",
    "had",
    "off",
    "with",
    "we",
    "we're",
    "we've",
    "we'll",
    "we'd",
    "went",
    "without",
    "on",
    "need",
    "should",
    "would",
    "could",
    "might",
    "may",
    "can",
    "must",
    "mustn't",
    "mustn",
    "mustnt",
    "ought",
    "oughtn't",
    "oughtnt",
    "gotta",
    "gotta go",
    "anything",
    "something",
    "ve",
    "was",
    "were",
    "sometime",
    "sometimes",
    "time to go",
}

TRIVIAL_FRENCH_VOCABULARY: set[str] = {
    "bonjour",
    "salut",
    "coucou",
    "bonsoir",
    "bonne nuit",
    "bonne journée",
    "bonne soirée",
    "bon matin",
    "bon après-midi",
    "comment allez-vous",
    "comment ça va",
    "ça va",
    "comment tu vas",
    "merci",
    "merci beaucoup",
    "merci bien",
    "je vous remercie",
    "je te remercie",
    "grand merci",
    "mille mercis",
    "de rien",
    "il n'y a pas de quoi",
    "je vous en prie",
    "je t'en prie",
    "avec plaisir",
    "tout le plaisir est pour moi",
    "pas de problème",
    "pas de souci",
    "aucun problème",
    "s'il vous plaît",
    "s'il te plaît",
    "svp",
    "excusez-moi",
    "excuse-moi",
    "pardon",
    "désolé",
    "désolée",
    "je suis désolé",
    "je suis désolée",
    "mes excuses",
    "toutes mes excuses",
    "au revoir",
    "à bientôt",
    "à plus tard",
    "à plus",
    "à tout à l'heure",
    "à demain",
    "bonne continuation",
    "portez-vous bien",
    "porte-toi bien",
    "prenez soin de vous",
    "prends soin de toi",
    "salut",
    "ciao",
    "tchao",
    "bye",
    "adieu",
    "oui",
    "si",
    "ouais",
    "d'accord",
    "ok",
    "okay",
    "très bien",
    "parfait",
    "excellent",
    "super",
    "génial",
    "formidable",
    "magnifique",
    "fantastique",
    "non",
    "nan",
    "pas du tout",
    "absolument pas",
    "jamais",
    "jamais de la vie",
    "hors de question",
    "pas question",
    "peut-être",
    "probablement",
    "sûrement",
    "certainement",
    "bien sûr",
    "évidemment",
    "naturellement",
    "effectivement",
    "exactement",
    "précisément",
    "tout à fait",
    "absolument",
    "complètement",
    "entièrement",
    "totalement",
    "vraiment",
    "réellement",
    "véritablement",
    "franchement",
    "sincèrement",
    "honnêtement",
    "je",
    "tu",
    "il",
    "elle",
    "nous",
    "vous",
    "ils",
    "elles",
    "le",
    "la",
    "les",
    "un",
    "une",
    "des",
    "du",
    "de",
    "à",
    "au",
    "aux",
    "dans",
    "sur",
    "sous",
    "avec",
    "sans",
    "pour",
    "par",
    "en",
    "et",
    "ou",
    "mais",
    "donc",
    "or",
    "ni",
    "car",
    "si",
    "que",
    "qui",
    "quoi",
    "où",
    "quand",
    "comment",
    "pourquoi",
    "combien",
    "quel",
    "quelle",
    "quels",
    "quelles",
    "ce",
    "cette",
    "ces",
    "cet",
    "mon",
    "ma",
    "mes",
    "ton",
    "ta",
    "tes",
    "son",
    "sa",
    "ses",
    "notre",
    "nos",
    "votre",
    "vos",
    "leur",
    "leurs",
    "être",
    "avoir",
    "faire",
    "aller",
    "venir",
    "voir",
    "savoir",
    "pouvoir",
    "vouloir",
    "devoir",
    "falloir",
    "dire",
    "prendre",
    "donner",
    "mettre",
    "partir",
    "sortir",
    "entrer",
    "rester",
    "arriver",
    "passer",
    "regarder",
    "écouter",
    "parler",
    "comprendre",
    "apprendre",
    "connaître",
    "reconnaître",
    "croire",
    "penser",
    "trouver",
    "chercher",
    "demander",
    "répondre",
    "attendre",
    "espérer",
    "aimer",
    "préférer",
    "détester",
    "adorer",
    "c'est",
    "c'était",
    "c'est ça",
    "voilà",
    "voici",
    "il y a",
    "il n'y a pas",
    "qu'est-ce que",
    "qu'est-ce qui",
    "est-ce que",
    "n'est-ce pas",
    "bien",
    "mal",
    "bon",
    "mauvais",
    "bonne",
    "mauvaise",
    "grand",
    "petit",
    "grande",
    "petite",
    "gros",
    "mince",
    "grosse",
    "beau",
    "belle",
    "joli",
    "jolie",
    "laid",
    "laide",
    "jeune",
    "vieux",
    "vieille",
    "nouveau",
    "nouvelle",
    "ancien",
    "ancienne",
    "premier",
    "première",
    "dernier",
    "dernière",
    "autre",
    "même",
    "tout",
    "toute",
    "tous",
    "toutes",
    "chaque",
    "plusieurs",
    "quelque",
    "quelques",
    "beaucoup",
    "peu",
    "assez",
    "trop",
    "très",
    "plus",
    "moins",
    "aussi",
    "autant",
    "encore",
    "déjà",
    "toujours",
    "souvent",
    "parfois",
    "quelquefois",
    "rarement",
    "jamais",
    "maintenant",
    "aujourd'hui",
    "hier",
    "demain",
    "avant",
    "après",
    "pendant",
    "depuis",
    "jusqu'à",
    "ici",
    "là",
    "là-bas",
    "partout",
    "nulle part",
    "quelque part",
    "ailleurs",
    "dehors",
    "dedans",
    "dessus",
    "dessous",
    "devant",
    "derrière",
    "à côté",
    "loin",
    "près",
    "autour",
    "entre",
    "parmi",
    "contre",
    "vers",
    "chez",
    "malgré",
    "selon",
    "sauf",
    "environ",
    "presque",
    "seulement",
    "même",
    "aussi",
    "non plus",
    "ne... pas",
    "ne... plus",
    "ne... jamais",
    "ne... rien",
    "ne... personne",
    "ne... que",
    "ne... guère",
    "ne... point",
    "quelqu'un",
    "quelque chose",
    "personne",
    "rien",
    "tout le monde",
    "n'importe qui",
    "n'importe quoi",
    "n'importe où",
    "n'importe quand",
    "n'importe comment",
    "tant mieux",
    "tant pis",
    "comme ça",
    "comme ci comme ça",
    "ça dépend",
    "ça va",
    "ça marche",
    "ça roule",
    "ça baigne",
    "ça gaze",
    "nickel",
    "impec",
    "cool",
    "sympa",
    "chouette",
    "génial",
    "top",
    "extra",
    "super",
    "hyper",
    "méga",
    "ultra",
    "archi",
    "vachement",
    "drôlement",
    "sacrément",
    "bigrement",
    "rudement",
    "fichement",
    "bougrement",
    "diablement",
    "terriblement",
    "affreusement",
    "horriblement",
    "épouvantablement",
    "incroyablement",
    "extraordinairement",
    "remarquablement",
    "particulièrement",
    "spécialement",
    "surtout",
    "notamment",
    "principalement",
    "essentiellement",
    "fondamentalement",
    "globalement",
    "généralement",
    "habituellement",
    "normalement",
    "ordinairement",
    "couramment",
    "fréquemment",
    "régulièrement",
    "constamment",
    "continuellement",
    "perpétuellement",
    "éternellement",
    "définitivement",
    "finalement",
    "enfin",
    "bref",
    "en fait",
    "en effet",
    "en réalité",
    "au fait",
    "à propos",
    "d'ailleurs",
    "par ailleurs",
    "en outre",
    "de plus",
    "en plus",
    "aussi",
    "également",
    "pareillement",
    "de même",
    "ainsi",
    "donc",
    "alors",
    "puis",
    "ensuite",
    "après",
    "enfin",
    "finalement",
    "d'abord",
    "premièrement",
    "deuxièmement",
    "troisièmement",
    "d'une part",
    "d'autre part",
    "par contre",
    "en revanche",
    "cependant",
    "néanmoins",
    "toutefois",
    "pourtant",
    "malgré tout",
    "quand même",
    "tout de même",
    "malgré cela",
    "en dépit de",
    "bien que",
    "quoique",
    "encore que",
    "même si",
    "sauf si",
    "à moins que",
    "pourvu que",
    "à condition que",
    "dans la mesure où",
    "étant donné que",
    "vu que",
    "puisque",
    "comme",
    "parce que",
    "car",
    "en raison de",
    "à cause de",
    "grâce à",
    "faute de",
    "par manque de",
    "par suite de",
    "suite à",
    "à la suite de",
    "par conséquent",
    "en conséquence",
    "de ce fait",
    "c'est pourquoi",
    "voilà pourquoi",
    "d'où",
    "de là",
    "par là",
    "de cette façon",
    "de cette manière",
    "ainsi",
    "comme ça",
    "comme cela",
    "de la sorte",
    "autrement dit",
    "c'est-à-dire",
    "en d'autres termes",
    "pour ainsi dire",
    "si l'on peut dire",
    "en quelque sorte",
    "d'une certaine façon",
    "d'une certaine manière",
    "en un sens",
    "dans un sens",
    "à vrai dire",
    "pour tout dire",
    "à dire vrai",
    "pour être franc",
    "pour être honnête",
    "franchement parlant",
    "honnêtement parlant",
    "entre nous",
    "soit dit en passant",
    "au passage",
    "en passant",
    "incidemment",
    "accessoirement",
    "subsidiairement",
    "secondairement",
    "marginalement",
    "superficiellement",
    "approximativement",
    "grossièrement",
    "vaguement",
    "confusément",
    "obscurément",
    "clairement",
    "nettement",
    "distinctement",
    "précisément",
    "exactement",
    "rigoureusement",
    "strictement",
    "littéralement",
    "textuellement",
    "mot pour mot",
    "au pied de la lettre",
    "à la lettre",
    "au sens propre",
    "au sens figuré",
    "métaphoriquement",
    "symboliquement",
    "allégoriquement",
    "ironiquement",
    "sarcastiquement",
    "humoristiquement",
    "plaisamment",
    "drôlement",
    "amusamment",
    "comiquement",
    "bizarrement",
    "étrangement",
    "curieusement",
    "singulièrement",
    "particulièrement",
    "spécialement",
    "exceptionnellement",
    "extraordinairement",
    "inhabituellement",
    "anormalement",
    "irrégulièrement",
    "illégalement",
    "légalement",
    "officiellement",
    "officieusement",
    "publiquement",
    "privément",
    "secrètement",
    "confidentiellement",
    "discrètement",
    "ouvertement",
    "franchement",
    "sincèrement",
    "honnêtement",
    "loyalement",
    "fidèlement",
    "scrupuleusement",
    "consciencieusement",
    "soigneusement",
    "attentivement",
    "minutieusement",
    "méticuleusement",
    "précautionneusement",
    "prudemment",
    "sagement",
    "raisonnablement",
    "logiquement",
    "rationnellement",
    "intelligemment",
    "astucieusement",
    "habilement",
    "adroitement",
    "savamment",
    "expertement",
    "professionnellement",
    "techniquement",
    "pratiquement",
    "concrètement",
    "effectivement",
    "réellement",
    "véritablement",
    "authentiquement",
    "genuinement",
    "sincèrement",
    "spontanément",
    "naturellement",
    "instinctivement",
    "intuitivement",
    "automatiquement",
    "mécaniquement",
    "systématiquement",
    "méthodiquement",
    "organisé",
    "structuré",
    "planifié",
    "prévu",
    "programmé",
    "calculé",
    "délibéré",
    "intentionnel",
    "volontaire",
    "conscient",
    "réfléchi",
    "pensé",
    "étudié",
    "analysé",
    "examiné",
    "observé",
    "regardé",
    "vu",
    "aperçu",
    "remarqué",
    "noté",
    "constaté",
    "découvert",
    "trouvé",
    "rencontré",
    "croisé",
    "tombé sur",
    "buté sur",
    "achoppé sur",
    "échoué sur",
    "raté",
    "manqué",
    "loupé",
    "foiré",
    "planté",
    "merdé",
    "déconné",
    "fait n'importe quoi",
    "n'importe quoi",
    "nawak",
    "ohlala",
    "oh là là",
    "mon dieu",
    "mon dieu",
    "seigneur",
    "jésus",
    "christ",
    "putain",
    "merde",
    "bordel",
    "zut",
    "flûte",
    "mince",
    "crotte",
    "purée",
    "saperlipopette",
    "sacrebleu",
    "morbleu",
    "palsambleu",
    "ventrebleu",
    "corbleu",
    "tudieu",
    "pardieu",
    "ma foi",
    "dame",
    "fichtre",
    "diantre",
    "que diable",
    "diable",
    "peste",
    "tonnerre",
    "sacré",
    "sacrée",
    "fichu",
    "fichue",
    "foutu",
    "foutue",
    "satané",
    "satanée",
    "maudit",
    "maudite",
    "damné",
    "damnée",
}

# Trivial conversation context constant
TRIVIAL_CONTEXT = "TRIVIAL"
