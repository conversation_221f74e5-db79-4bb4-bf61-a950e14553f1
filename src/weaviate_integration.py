"""
Weaviate Integration Module for OmniRAG

This module provides comprehensive Weaviate database support including:
- Index creation and management
- Document ingestion with metadata
- Query and retrieval operations
- Support for both Weaviate v3 and v4 APIs
"""

import asyncio
import logging
import warnings
from typing import List, Dict, Any, Optional, Tuple, Union

import numpy as np
import weaviate
from weaviate.classes.config import Configure

# Silence protobuf warnings
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message=r"Protobuf gencode version .* is exactly one major version older than the runtime version .*",
)

logger = logging.getLogger(__name__)


class WeaviateManager:
    """Manages Weaviate connections and operations for both v3 and v4 APIs"""
    
    def __init__(self, host: str = "localhost", port: int = 8080, use_ssl: bool = False):
        """Initialize Weaviate manager
        
        Parameters
        ----------
        host : str, optional
            Weaviate host, by default "localhost"
        port : int, optional
            Weaviate port, by default 8080
        use_ssl : bool, optional
            Whether to use SSL, by default False
        """
        self.host = host
        self.port = port
        self.use_ssl = use_ssl
        self.client = None
        self.api_version = None
        self.collection_name = None
        
    def connect(self) -> bool:
        """Connect to Weaviate instance

        Returns
        -------
        bool
            True if connection successful, False otherwise
        """
        # Try v4 API first
        if self.host == "localhost" and self.port == 8080:
            self.client = weaviate.connect_to_local()
        else:
            self.client = weaviate.connect_to_custom(
                http_host=self.host,
                http_port=self.port,
                http_secure=self.use_ssl
            )

        # Check if v4 API connection worked
        if hasattr(self.client, "collections"):
            self.api_version = "v4"
            logger.info("Connected to Weaviate using v4 API")
            return True
        else:
            # Fallback to v3 API
            logger.info("Weaviate v4 connection failed, trying v3 API...")
            scheme = "https" if self.use_ssl else "http"
            url = f"{scheme}://{self.host}:{self.port}"
            self.client = weaviate.Client(url)
            self.api_version = "v3"
            logger.info("Connected to Weaviate using v3 API")
            return True
    
    def disconnect(self):
        """Disconnect from Weaviate"""
        if self.client and hasattr(self.client, 'close'):
            self.client.close()
            logger.info("Disconnected from Weaviate")
    
    def create_collection(
        self,
        collection_name: str,
        vectorizer_config: Optional[Dict] = None,
        properties: Optional[List[Dict]] = None
    ) -> bool:
        """Create a collection/class in Weaviate

        Parameters
        ----------
        collection_name : str
            Name of the collection/class to create
        vectorizer_config : Optional[Dict], optional
            Vectorizer configuration, by default None
        properties : Optional[List[Dict]], optional
            Property definitions, by default None

        Returns
        -------
        bool
            True if creation successful, False otherwise
        """
        if self.api_version == "v4":
            # Check if collection already exists
            if self.client.collections.exists(collection_name):
                logger.info(f"Collection '{collection_name}' already exists")
                self.collection_name = collection_name
                return True

            # Default vectorizer config for v4
            if vectorizer_config is None:
                vector_config = Configure.Vectors.text2vec_ollama(
                    api_endpoint="http://ollama:11434",
                    model="nomic-embed-text",
                )
            else:
                vector_config = vectorizer_config

            # Create collection
            self.client.collections.create(
                name=collection_name,
                vector_config=vector_config,
            )
            logger.info(f"Created Weaviate collection: {collection_name}")

        else:  # v3 API
            # Check if class already exists
            if self.client.schema.contains(collection_name):
                logger.info(f"Class '{collection_name}' already exists")
                self.collection_name = collection_name
                return True

            # Default class definition for v3
            class_definition = {
                "class": collection_name,
                "vectorizer": "none" if vectorizer_config is None else vectorizer_config.get("vectorizer", "none"),
            }

            if properties:
                class_definition["properties"] = properties

            self.client.schema.create_class(class_definition)
            logger.info(f"Created Weaviate class: {collection_name}")

        self.collection_name = collection_name
        return True
    
    def delete_collection(self, collection_name: str) -> bool:
        """Delete a collection/class from Weaviate

        Parameters
        ----------
        collection_name : str
            Name of the collection/class to delete

        Returns
        -------
        bool
            True if deletion successful, False otherwise
        """
        if self.api_version == "v4":
            if self.client.collections.exists(collection_name):
                self.client.collections.delete(collection_name)
                logger.info(f"Deleted Weaviate collection: {collection_name}")
                return True
            else:
                logger.warning(f"Collection '{collection_name}' does not exist")
                return False
        else:  # v3 API
            if self.client.schema.contains(collection_name):
                self.client.schema.delete_class(collection_name)
                logger.info(f"Deleted Weaviate class: {collection_name}")
                return True
            else:
                logger.warning(f"Class '{collection_name}' does not exist")
                return False
    
    def ingest_documents(
        self,
        documents: List[Dict[str, Any]],
        collection_name: Optional[str] = None,
        batch_size: int = 50
    ) -> Tuple[int, int]:
        """Ingest documents into Weaviate

        Parameters
        ----------
        documents : List[Dict[str, Any]]
            List of documents to ingest
        collection_name : Optional[str], optional
            Collection name to use, by default None (uses self.collection_name)
        batch_size : int, optional
            Batch size for ingestion, by default 50

        Returns
        -------
        Tuple[int, int]
            (successful_count, failed_count)
        """
        if collection_name is None:
            collection_name = self.collection_name

        if not collection_name:
            logger.error("No collection name specified")
            return 0, len(documents)

        successful = 0
        failed = 0

        if self.api_version == "v4":
            collection = self.client.collections.get(collection_name)

            with collection.batch.fixed_size(batch_size=batch_size) as batch:
                for i, doc in enumerate(documents):
                    batch.add_object(doc)
                    if (i + 1) % 100 == 0:
                        logger.info(f"Ingested {i + 1}/{len(documents)} documents")

            failed_objects = collection.batch.failed_objects
            failed = len(failed_objects)
            successful = len(documents) - failed

        else:  # v3 API
            with self.client.batch as batch:
                batch.batch_size = batch_size
                for i, doc in enumerate(documents):
                    batch.add_data_object(doc, collection_name)
                    if (i + 1) % 100 == 0:
                        logger.info(f"Ingested {i + 1}/{len(documents)} documents")

            # For v3, we assume all succeeded unless there's an exception
            successful = len(documents)
            failed = 0

        logger.info(f"Ingestion complete - Success: {successful}, Failed: {failed}")
        return successful, failed

    async def query_near_text(
        self,
        query: str,
        limit: int = 5,
        collection_name: Optional[str] = None,
        return_metadata: bool = True
    ) -> List[Dict[str, Any]]:
        """Query Weaviate using near_text

        Parameters
        ----------
        query : str
            Text query
        limit : int, optional
            Number of results to return, by default 5
        collection_name : Optional[str], optional
            Collection name to query, by default None
        return_metadata : bool, optional
            Whether to return metadata, by default True

        Returns
        -------
        List[Dict[str, Any]]
            Query results
        """
        if collection_name is None:
            collection_name = self.collection_name

        if not collection_name:
            logger.error("No collection name specified")
            return []

        if self.api_version == "v4":
            collection = self.client.collections.get(collection_name)

            if return_metadata:
                response = await asyncio.to_thread(
                    collection.query.near_text,
                    query=query,
                    limit=limit,
                    return_metadata=["score", "distance"]
                )
            else:
                response = await asyncio.to_thread(
                    collection.query.near_text,
                    query=query,
                    limit=limit
                )

            results = []
            for obj in response.objects:
                result = dict(obj.properties)
                if return_metadata and hasattr(obj, 'metadata'):
                    result['_metadata'] = {
                        'id': str(obj.uuid),
                        'score': getattr(obj.metadata, 'score', None),
                        'distance': getattr(obj.metadata, 'distance', None)
                    }
                results.append(result)

            return results

            else:  # v3 API
                # Try different field names for content
                content_fields = ["text", "page_content", "content"]

                for field in content_fields:
                    query_builder = (
                        self.client.query.get(collection_name, [field, "chunk_index", "source"])
                        .with_near_text({"concepts": [query]})
                        .with_limit(limit)
                    )

                    if return_metadata:
                        query_builder = query_builder.with_additional(["score", "distance", "id"])

                    response = await asyncio.to_thread(query_builder.do)

                    if response and "data" in response and "Get" in response["data"]:
                        objects = response["data"]["Get"].get(collection_name, [])

                        if objects and any(obj.get(field) for obj in objects):
                            results = []
                            for obj in objects:
                                result = {k: v for k, v in obj.items() if not k.startswith("_")}
                                if return_metadata and "_additional" in obj:
                                    result['_metadata'] = obj["_additional"]
                                results.append(result)
                            return results

                return []

    async def query_near_vector(
        self,
        vector: Union[List[float], np.ndarray],
        limit: int = 5,
        collection_name: Optional[str] = None,
        return_metadata: bool = True
    ) -> List[Dict[str, Any]]:
        """Query Weaviate using near_vector

        Parameters
        ----------
        vector : Union[List[float], np.ndarray]
            Query vector
        limit : int, optional
            Number of results to return, by default 5
        collection_name : Optional[str], optional
            Collection name to query, by default None
        return_metadata : bool, optional
            Whether to return metadata, by default True

        Returns
        -------
        List[Dict[str, Any]]
            Query results
        """
        if collection_name is None:
            collection_name = self.collection_name

        if not collection_name:
            logger.error("No collection name specified")
            return []

        # Convert numpy array to list if needed
        if isinstance(vector, np.ndarray):
            vector = vector.tolist()

        if self.api_version == "v4":
            collection = self.client.collections.get(collection_name)

            if return_metadata:
                response = await asyncio.to_thread(
                    collection.query.near_vector,
                    near_vector=vector,
                    limit=limit,
                    return_metadata=["score", "distance"]
                )
            else:
                response = await asyncio.to_thread(
                    collection.query.near_vector,
                    near_vector=vector,
                    limit=limit
                )

            results = []
            for obj in response.objects:
                result = dict(obj.properties)
                if return_metadata and hasattr(obj, 'metadata'):
                    result['_metadata'] = {
                        'id': str(obj.uuid),
                        'score': getattr(obj.metadata, 'score', None),
                        'distance': getattr(obj.metadata, 'distance', None)
                    }
                results.append(result)

            return results

        else:  # v3 API
            # Try different field names for content
            content_fields = ["text", "page_content", "content"]

            for field in content_fields:
                query_builder = (
                    self.client.query.get(collection_name, [field, "chunk_index", "source"])
                    .with_near_vector({"vector": vector})
                    .with_limit(limit)
                )

                if return_metadata:
                    query_builder = query_builder.with_additional(["score", "distance", "id"])

                response = await asyncio.to_thread(query_builder.do)

                if response and "data" in response and "Get" in response["data"]:
                    objects = response["data"]["Get"].get(collection_name, [])

                    if objects and any(obj.get(field) for obj in objects):
                        results = []
                        for obj in objects:
                            result = {k: v for k, v in obj.items() if not k.startswith("_")}
                            if return_metadata and "_additional" in obj:
                                result['_metadata'] = obj["_additional"]
                            results.append(result)
                        return results

            return []

    def get_collection_info(self, collection_name: Optional[str] = None) -> Dict[str, Any]:
        """Get information about a collection/class

        Parameters
        ----------
        collection_name : Optional[str], optional
            Collection name, by default None

        Returns
        -------
        Dict[str, Any]
            Collection information
        """
        if collection_name is None:
            collection_name = self.collection_name

        if not collection_name:
            logger.error("No collection name specified")
            return {}

        if self.api_version == "v4":
            if self.client.collections.exists(collection_name):
                collection = self.client.collections.get(collection_name)
                # Get basic info - v4 API doesn't expose as much metadata
                return {
                    "name": collection_name,
                    "exists": True,
                    "api_version": "v4"
                }
            else:
                return {"name": collection_name, "exists": False, "api_version": "v4"}
        else:  # v3 API
            if self.client.schema.contains(collection_name):
                schema = self.client.schema.get(collection_name)
                return {
                    "name": collection_name,
                    "exists": True,
                    "api_version": "v3",
                    "schema": schema
                }
            else:
                return {"name": collection_name, "exists": False, "api_version": "v3"}

    def list_collections(self) -> List[str]:
        """List all collections/classes

        Returns
        -------
        List[str]
            List of collection/class names
        """
        if self.api_version == "v4":
            # v4 API doesn't have a direct list method, so we'll return known collections
            # This is a limitation of the current v4 API
            known_collections = []
            for name in ["Question", "Document", "Chunk"]:
                if self.client.collections.exists(name):
                    known_collections.append(name)
            return known_collections
        else:  # v3 API
            schema = self.client.schema.get()
            return [cls["class"] for cls in schema.get("classes", [])]
