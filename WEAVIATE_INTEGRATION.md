# Weaviate Integration for OmniRAG

This document describes the comprehensive Weaviate database integration added to OmniRAG, providing support for both Weaviate v3 and v4 APIs with automatic fallback capabilities.

## Overview

The Weaviate integration includes:

- **Dual API Support**: Automatic detection and support for both Weaviate v3 and v4 APIs
- **Enhanced Retrieval**: Improved integration with the EnsembleRetriever for hybrid search
- **Flexible Collection Management**: Support for different collection/class names ("Question", "Document")
- **Comprehensive Utilities**: Index building, querying, and management tools
- **Backward Compatibility**: Existing code continues to work with improvements

## Architecture

### Core Components

1. **WeaviateManager** (`src/weaviate_integration.py`)
   - Handles connections to both v3 and v4 APIs
   - Manages collections/classes creation and deletion
   - Provides unified query interface

2. **WeaviateIndexBuilder** (`src/weaviate_builder.py`)
   - Builds indices from various document sources (PDF, text, raw documents)
   - Handles document chunking and metadata extraction
   - Supports batch ingestion

3. **WeaviateQueryInterface** (`src/weaviate_query.py`)
   - Provides high-level query interface
   - Supports both text and vector queries
   - Formats results for display

4. **Enhanced EnsembleRetriever** (`src/ensembleretriever.py`)
   - Added `_get_weaviate_scores()` method for proper Weaviate integration
   - Supports both v3 and v4 API query patterns
   - Handles different field names automatically

### Updated Chain Classes

All custom chain classes have been updated to support the new Weaviate integration:

- `src/customchain.py`
- `src/customchain_naive.py`
- `src/customchainmixedhah.py`
- `src/embeddingloader.py`

## Setup and Configuration

### Prerequisites

1. **Weaviate Server**: Running on `localhost:8080` (default)
2. **Python Dependencies**: 
   ```bash
   pip install weaviate-client
   ```
3. **Optional Dependencies** (for document processing):
   ```bash
   pip install pypdf python-docx
   ```

### Docker Setup

Use the provided docker-compose configuration:

```bash
cd weaviate/
docker-compose up -d
```

This starts Weaviate with:
- Text2vec-ollama vectorizer
- Nomic-embed-text model
- Persistent storage

## Usage Examples

### Building an Index

```python
from src.weaviate_builder import WeaviateIndexBuilder
import asyncio

async def build_index():
    # Initialize builder
    builder = WeaviateIndexBuilder()
    
    # Connect to Weaviate
    if not builder.connect():
        print("Failed to connect to Weaviate")
        return
    
    # Create collection
    builder.create_collection(
        collection_name="Question",
        vectorizer_model="nomic-embed-text",
        recreate=True
    )
    
    # Process documents
    documents = [
        "Artificial intelligence is transforming technology...",
        "Machine learning enables computers to learn...",
        # ... more documents
    ]
    
    processed_docs = builder.process_documents(documents)
    
    # Build index
    success = await builder.build_index(
        documents=processed_docs,
        collection_name="Question"
    )
    
    builder.disconnect()
    return success

# Run the example
asyncio.run(build_index())
```

### Querying the Index

```python
from src.weaviate_query import WeaviateQueryInterface
import asyncio

async def query_index():
    # Initialize query interface
    query_interface = WeaviateQueryInterface()
    
    # Connect and query
    if query_interface.connect():
        results = await query_interface.search_text(
            query="machine learning",
            limit=5,
            collection_name="Question"
        )
        
        # Format and display results
        formatted = query_interface.format_results(results)
        print(formatted)
        
        query_interface.disconnect()

# Run the query
asyncio.run(query_index())
```

### Using in OmniRAG Pipeline

```python
from src.customchain import CustomLLMChain
from src.globalvariables import IndexType

# Initialize with Weaviate
chain = CustomLLMChain(
    tokenizer=tokenizer,
    model=model,
    model_name="your-model",
    vector_store_name="your-store",
    index_type=IndexType.WEAVIATE
)

# Use normally - Weaviate integration is automatic
answer, context, metrics = chain.ainvoke("What is machine learning?")
```

## API Compatibility

### Weaviate v4 API (Preferred)

The integration automatically detects and uses v4 API when available:

```python
# v4 API usage (automatic)
client = weaviate.connect_to_local()
collection = client.collections.get("Question")
response = collection.query.near_text(query="biology", limit=5)
```

### Weaviate v3 API (Fallback)

Falls back to v3 API if v4 is not available:

```python
# v3 API usage (automatic fallback)
client = weaviate.Client("http://localhost:8080")
results = client.query.get("Question", ["text"]).with_near_text({"concepts": ["biology"]}).do()
```

## Configuration Options

### Collection Settings

- **Collection Names**: Supports "Question" and "Document" collections
- **Vectorizer**: Configurable (default: text2vec-ollama with nomic-embed-text)
- **Field Names**: Automatically detects "text", "page_content", "content" fields

### Chunking Options

- **Chunk Size**: Default 200 words, configurable
- **Overlap**: Default 20 words for context preservation
- **Methods**: Word-based or sentence-based chunking

### Batch Processing

- **Batch Size**: Default 50 documents, configurable
- **Error Handling**: Continues processing on individual failures
- **Progress Tracking**: Logs progress for large datasets

## Advanced Features

### Metadata Support

Documents are automatically enriched with metadata:

```python
{
    "text": "Document content...",
    "chunk_index": 0,
    "source": "document.pdf",
    "document_type": "pdf",
    "total_chunks": 10,
    "_metadata": {
        "score": 0.95,
        "distance": 0.05,
        "id": "uuid-string"
    }
}
```

### Hybrid Search Integration

The EnsembleRetriever automatically combines:
- BM25 keyword search
- Weaviate semantic search
- Configurable fusion methods

### Error Handling and Fallbacks

- Automatic API version detection
- Graceful fallback between v4 and v3 APIs
- Multiple field name attempts
- Connection retry logic

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Ensure Weaviate is running on localhost:8080
   - Check docker-compose logs: `docker-compose logs weaviate`

2. **Collection Not Found**
   - Run the index builder first
   - Check available collections: `query_interface.list_collections()`

3. **Empty Results**
   - Verify documents were indexed successfully
   - Check collection info: `query_interface.get_collection_info()`

4. **API Version Issues**
   - The integration handles this automatically
   - Check logs for API version detection messages

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance Considerations

### Indexing Performance

- Use appropriate batch sizes (50-100 documents)
- Consider chunking strategy for large documents
- Monitor memory usage during large ingestions

### Query Performance

- Weaviate v4 API generally performs better
- Use appropriate limit values
- Consider caching for repeated queries

### Resource Usage

- Weaviate requires sufficient memory for embeddings
- Docker container should have adequate resources
- Monitor disk usage for persistent storage

## Migration Guide

### From Existing Weaviate Integration

The new integration is backward compatible. Existing code will continue to work with these improvements:

1. **Automatic API Detection**: No code changes needed
2. **Enhanced Error Handling**: Better reliability
3. **Improved Retrieval**: Better search results
4. **Flexible Collection Names**: Supports both "Question" and "Document"

### Recommended Updates

While not required, consider these updates for better performance:

1. Use the new `WeaviateIndexBuilder` for index creation
2. Use `WeaviateQueryInterface` for standalone querying
3. Update collection names to "Question" for consistency with examples

## Examples and Testing

### Running Examples

1. **Build Index Example**:
   ```bash
   python examples/weaviate_build_example.py
   ```

2. **Query Example**:
   ```bash
   python src/weaviate_query.py
   ```

3. **Integration Test**:
   ```bash
   # Use Weaviate in your existing OmniRAG pipeline
   # Set index_type=IndexType.WEAVIATE
   ```

### Sample Data

The examples include sample biology-related documents for testing. You can also use your own PDFs or text files.

## Future Enhancements

Planned improvements include:

- Support for additional vectorizers
- Advanced filtering capabilities
- Batch query operations
- Performance monitoring and metrics
- Integration with more document types

## Support

For issues or questions:

1. Check the troubleshooting section above
2. Review Weaviate documentation: https://weaviate.io/developers/weaviate
3. Check OmniRAG logs for detailed error messages
