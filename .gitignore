# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
.cache/

# Jupyter Notebook
.ipynb_checkpoints

# Spyder project settings
.spyderproject
.spyproject

# PyCharm
.idea/

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Environments
.env
.venv
env/
venv/
ENV/

# Generated vector database
vector_store/*
.safetensors
.json
*.db

# Models
src/models

# Data
data/multilingual_stopwords.pkl
.trash
pdfs/
*.mdc
*.zip

# Claude Flow generated files
.claude/settings.local.json
.mcp.json
claude-flow.config.json
.swarm/
.hive-mind/
memory/claude-flow-data.json
memory/sessions/*
!memory/sessions/README.md
memory/agents/*
!memory/agents/README.md
coordination/memory_bank/*
coordination/subtasks/*
coordination/orchestration/*
*.db
*.db-journal
*.db-wal
*.sqlite
*.sqlite-journal
*.sqlite-wal
claude-flow
claude-flow.bat
claude-flow.ps1
hive-mind-prompt-*.txt
CLAUDE*
.roo*
.claude*
memory*
pklv.py
cf-ccr*
setup*