#!/usr/bin/env python3
"""
Example: Building a Weaviate Index for OmniRAG

This example demonstrates how to build a Weaviate index from various document sources
including PDFs, text files, and raw text documents.

Usage:
    python examples/weaviate_build_example.py
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the src directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.weaviate_builder import WeaviateIndexBuilder


async def build_from_pdf_example():
    """Example: Build index from a PDF file"""
    print("\n" + "="*60)
    print("Building Weaviate Index from PDF")
    print("="*60)
    
    # Initialize the builder
    builder = WeaviateIndexBuilder()
    
    # Connect to Weaviate
    if not builder.connect():
        print("❌ Failed to connect to Weaviate. Make sure it's running on localhost:8080")
        return False
    
    print("✅ Connected to Weaviate")
    
    try:
        # Create collection (this will use the new API automatically)
        collection_name = "PDFDocuments"
        if not builder.create_collection(
            collection_name=collection_name,
            recreate=True  # Recreate if exists
        ):
            print(f"❌ Failed to create collection '{collection_name}'")
            return False
        
        print(f"✅ Created collection '{collection_name}'")
        
        # Process PDF file (you'll need to provide a real PDF path)
        pdf_path = "example_document.pdf"  # Change this to your PDF path
        
        if not Path(pdf_path).exists():
            print(f"⚠️  PDF file not found: {pdf_path}")
            print("Creating sample text documents instead...")
            
            # Create sample documents
            sample_docs = [
                "Artificial intelligence is transforming the way we work and live. Machine learning algorithms can now process vast amounts of data to identify patterns and make predictions.",
                "Deep learning, a subset of machine learning, uses neural networks with multiple layers to learn complex representations of data. This has led to breakthroughs in computer vision and natural language processing.",
                "Natural language processing enables computers to understand, interpret, and generate human language. Applications include chatbots, translation services, and sentiment analysis.",
                "Computer vision allows machines to interpret and understand visual information from the world. This technology is used in autonomous vehicles, medical imaging, and facial recognition systems.",
                "Data science combines statistics, programming, and domain expertise to extract insights from data. It involves data collection, cleaning, analysis, and visualization."
            ]
            
            documents = builder.process_documents(sample_docs)
        else:
            # Process the PDF
            print(f"📄 Processing PDF: {pdf_path}")
            documents = builder.process_pdf(pdf_path)
        
        print(f"📝 Processed {len(documents)} document chunks")
        
        # Build the index
        print("🔨 Building Weaviate index...")
        success = await builder.build_index(
            documents=documents,
            collection_name=collection_name,
            batch_size=50
        )
        
        if success:
            print("✅ Successfully built Weaviate index!")
            return True
        else:
            print("❌ Failed to build index")
            return False
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        builder.disconnect()


async def build_from_text_files_example():
    """Example: Build index from text files"""
    print("\n" + "="*60)
    print("Building Weaviate Index from Text Files")
    print("="*60)
    
    # Initialize the builder
    builder = WeaviateIndexBuilder()
    
    # Connect to Weaviate
    if not builder.connect():
        print("❌ Failed to connect to Weaviate")
        return False
    
    print("✅ Connected to Weaviate")
    
    try:
        # Create collection
        collection_name = "TextDocuments"
        if not builder.create_collection(
            collection_name=collection_name,
            recreate=True
        ):
            print(f"❌ Failed to create collection '{collection_name}'")
            return False
        
        print(f"✅ Created collection '{collection_name}'")
        
        # Create sample text files if they don't exist
        sample_files = []
        for i, content in enumerate([
            "Machine learning is a method of data analysis that automates analytical model building. It is a branch of artificial intelligence based on the idea that systems can learn from data, identify patterns and make decisions with minimal human intervention.",
            "Deep learning is part of a broader family of machine learning methods based on artificial neural networks with representation learning. Learning can be supervised, semi-supervised or unsupervised.",
            "Natural language processing is a subfield of linguistics, computer science, and artificial intelligence concerned with the interactions between computers and human language, in particular how to program computers to process and analyze large amounts of natural language data."
        ], 1):
            file_path = f"sample_text_{i}.txt"
            with open(file_path, 'w') as f:
                f.write(content)
            sample_files.append(file_path)
        
        # Process text files
        all_documents = []
        for file_path in sample_files:
            print(f"📄 Processing: {file_path}")
            docs = builder.process_text_file(file_path)
            all_documents.extend(docs)
            
            # Clean up sample file
            Path(file_path).unlink()
        
        print(f"📝 Processed {len(all_documents)} document chunks")
        
        # Build the index
        print("🔨 Building Weaviate index...")
        success = await builder.build_index(
            documents=all_documents,
            collection_name=collection_name,
            batch_size=50
        )
        
        if success:
            print("✅ Successfully built Weaviate index!")
            return True
        else:
            print("❌ Failed to build index")
            return False
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        builder.disconnect()


async def build_custom_collection_example():
    """Example: Build a custom collection with specific settings"""
    print("\n" + "="*60)
    print("Building Custom Weaviate Collection")
    print("="*60)
    
    # Initialize the builder
    builder = WeaviateIndexBuilder()
    
    # Connect to Weaviate
    if not builder.connect():
        print("❌ Failed to connect to Weaviate")
        return False
    
    print("✅ Connected to Weaviate")
    
    try:
        # Create a custom collection with specific settings
        collection_name = "Question"  # This matches the example in weaviate/
        
        if not builder.create_collection(
            collection_name=collection_name,
            vectorizer_model="nomic-embed-text",
            ollama_endpoint="http://ollama:11434",
            recreate=True
        ):
            print(f"❌ Failed to create collection '{collection_name}'")
            return False
        
        print(f"✅ Created collection '{collection_name}' with custom settings")
        
        # Create comprehensive sample documents
        sample_documents = [
            "Biology is the scientific study of life and living organisms. It encompasses various fields including molecular biology, genetics, ecology, and evolutionary biology.",
            "Cell biology focuses on the structure and function of cells, which are the basic units of life. Cells contain organelles that perform specific functions necessary for survival.",
            "Genetics is the study of heredity and the variation of inherited characteristics. DNA contains the genetic instructions for the development and function of living things.",
            "Ecology examines the relationships between organisms and their environment. It studies how organisms interact with each other and with their physical surroundings.",
            "Evolution is the process by which different kinds of living organisms develop and diversify from earlier forms during the history of the earth.",
            "Photosynthesis is the process by which plants and other organisms convert light energy into chemical energy that can be later released to fuel the organism's activities.",
            "The nervous system is a complex network of nerves and cells that carry messages to and from the brain and spinal cord to various parts of the body.",
            "Protein synthesis is the process by which cells build proteins. It involves transcription of DNA to RNA and translation of RNA to proteins.",
            "Mitosis is a type of cell division that results in two daughter cells each having the same number and kind of chromosomes as the parent nucleus.",
            "Biodiversity refers to the variety of life on Earth at all its levels, from genes to ecosystems, and the ecological and evolutionary processes that sustain it."
        ]
        
        # Process the documents
        documents = builder.process_documents(sample_documents)
        print(f"📝 Processed {len(documents)} document chunks")
        
        # Build the index
        print("🔨 Building Weaviate index...")
        success = await builder.build_index(
            documents=documents,
            collection_name=collection_name,
            batch_size=25  # Smaller batch size for this example
        )
        
        if success:
            print("✅ Successfully built custom Weaviate collection!")
            print(f"🔍 You can now query this collection using the query examples")
            return True
        else:
            print("❌ Failed to build index")
            return False
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        builder.disconnect()


async def main():
    """Main function to run all examples"""
    print("🚀 Weaviate Index Building Examples")
    print("Make sure Weaviate is running (docker-compose up -d)")
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s"
    )
    
    # Run examples
    examples = [
        ("Custom Collection (Biology)", build_custom_collection_example),
        ("PDF Documents", build_from_pdf_example),
        ("Text Files", build_from_text_files_example),
    ]
    
    for name, example_func in examples:
        print(f"\n🔄 Running example: {name}")
        try:
            success = await example_func()
            if success:
                print(f"✅ {name} example completed successfully")
            else:
                print(f"❌ {name} example failed")
        except Exception as e:
            print(f"❌ {name} example error: {e}")
    
    print("\n🎉 All examples completed!")
    print("\nNext steps:")
    print("1. Run 'python src/weaviate_query.py' to test querying")
    print("2. Use the Weaviate integration in your OmniRAG pipeline")


if __name__ == "__main__":
    asyncio.run(main())
