import json
import weaviate

print("[retrieve] Connecting to <PERSON>avia<PERSON> at http://localhost:8080 ...")
client = weaviate.connect_to_local()

questions = client.collections.get("Question")

query_text = "biology"  # change this to your query
limit = 5
print(f"[retrieve] Running near_text query: '{query_text}' (limit={limit}) ...")

response = questions.query.near_text(
    query=query_text,
    limit=limit,
)

objs = response.objects or []
print(f"[retrieve] Retrieved {len(objs)} object(s)")

for i, obj in enumerate(objs, start=1):
    print(f"\n[retrieve] Result #{i}")
    print(json.dumps(obj.properties, indent=2))

client.close()
print("[retrieve] Done.")