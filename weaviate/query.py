import json
import sys
from pathlib import Path
import asyncio

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.weaviate_integration import WeaviateManager

async def main():
    print("[retrieve] Connecting to Weaviate at http://localhost:8080 ...")

    # Use the new WeaviateManager for better compatibility
    weaviate_manager = WeaviateManager()
    if not weaviate_manager.connect():
        print("[retrieve] Failed to connect to Weaviate")
        return

    print(f"[retrieve] Connected using Weaviate {weaviate_manager.api_version} API")

    # Check available collections
    collections = weaviate_manager.list_collections()
    print(f"[retrieve] Available collections: {collections}")

    if not collections:
        print("[retrieve] No collections found. Please run build_index.py first.")
        weaviate_manager.disconnect()
        return

    # Use the first available collection (prefer "Question")
    collection_name = "Question" if "Question" in collections else collections[0]
    print(f"[retrieve] Using collection: {collection_name}")

    # Test queries
    test_queries = ["biology", "cell", "genetics", "evolution"]

    for query_text in test_queries:
        limit = 3
        print(f"\n[retrieve] Running near_text query: '{query_text}' (limit={limit}) ...")

        # Use the unified query interface
        results = await weaviate_manager.query_near_text(
            query=query_text,
            limit=limit,
            collection_name=collection_name,
            return_metadata=True
        )

        print(f"[retrieve] Retrieved {len(results)} object(s)")

        for i, result in enumerate(results, start=1):
            print(f"\n[retrieve] Result #{i}")

            # Extract and display the main content
            text_content = (
                result.get("text") or
                result.get("page_content") or
                result.get("content") or
                "No text content found"
            )

            print(f"Text: {text_content[:200]}{'...' if len(text_content) > 200 else ''}")

            # Show metadata if available
            if "_metadata" in result:
                metadata = result["_metadata"]
                if "score" in metadata:
                    print(f"Score: {metadata['score']:.4f}")
                if "distance" in metadata:
                    print(f"Distance: {metadata['distance']:.4f}")

            # Show source information
            if "source" in result:
                print(f"Source: {result['source']}")
            if "chunk_index" in result:
                print(f"Chunk: {result['chunk_index']}")

    weaviate_manager.disconnect()
    print("\n[retrieve] Done.")

if __name__ == "__main__":
    asyncio.run(main())