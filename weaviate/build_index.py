import warnings
# Silence only the protobuf gencode/runtime minor version UserWarning
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message=r"Protobuf gencode version .* is exactly one major version older than the runtime version .*",
)

import re
import weaviate
from weaviate.classes.config import Configure
from pypdf import PdfReader

# Build step: (re)create the collection and ingest documents
print("[index] Connecting to Weaviate at http://localhost:8080 ...")
client = weaviate.connect_to_local()

# Drop existing collection to rebuild a clean index
if client.collections.exists("Question"):
    print("[index] Existing collection 'Question' found — deleting it ...")
    client.collections.delete("Question")
else:
    print("[index] No existing 'Question' collection — creating fresh index ...")

# Create collection with embeddings (no generative inference)
print("[index] Creating collection 'Question' with vectorizer text2vec-ollama (nomic-embed-text) ...")
client.collections.create(
    name="Question",
    vector_config=Configure.Vectors.text2vec_ollama(
        api_endpoint="http://ollama:11434",
        model="nomic-embed-text",
    ),
)

# Read local PDF and chunk into fixed 200-word segments
pdf_path = "main__.pdf"
print(f"[index] Reading PDF: {pdf_path} ...")
reader = PdfReader(pdf_path)
num_pages = len(reader.pages)
print(f"[index] PDF pages: {num_pages}")

full_text_parts = []
for page_idx, page in enumerate(reader.pages, start=1):
    text = page.extract_text() or ""
    full_text_parts.append(text)
    if page_idx % 5 == 0 or page_idx == num_pages:
        print(f"[index] Extracted page {page_idx}/{num_pages}")

full_text = "\n".join(full_text_parts)
words = re.findall(r"\S+", full_text)
chunk_size = 200
chunks = [
    " ".join(words[i : i + chunk_size]) for i in range(0, len(words), chunk_size)
]
non_empty_chunks = [(i, t) for i, t in enumerate(chunks) if t.strip()]
num_chunks = len(non_empty_chunks)
print(f"[index] Total words: {len(words)} | Chunks (@{chunk_size} words): {num_chunks}")

questions = client.collections.get("Question")

print("[index] Ingesting chunks ...")
ingested = 0
with questions.batch.fixed_size(batch_size=50) as batch:
    for idx, (orig_idx, chunk_text) in enumerate(non_empty_chunks, start=1):
        batch.add_object(
            {
                "text": chunk_text,
                "chunk_index": orig_idx,
                "source": pdf_path,
            }
        )
        ingested += 1
        if idx % 100 == 0 or idx == num_chunks:
            print(f"[index] Ingested {idx}/{num_chunks} chunks")
        if batch.number_errors > 10:
            print("[index] Batch import stopped due to excessive errors (>10).")
            break

failed_objects = questions.batch.failed_objects
num_failed = len(failed_objects)
num_success = max(0, ingested - num_failed)
print(f"[index] Ingestion summary — success: {num_success}, failed: {num_failed}")
if num_failed:
    print(f"[index] First failed object: {failed_objects[0]}")

client.close()
print("[index] Done.")