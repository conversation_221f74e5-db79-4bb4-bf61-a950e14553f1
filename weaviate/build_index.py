import re
import sys
import warnings
from pathlib import Path

import weaviate
from weaviate.classes.config import Configure

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.weaviate_integration import WeaviateManager

# Optional import - check if pypdf is available
pypdf_available = True
try:
    from pypdf import PdfReader
except ImportError:
    pypdf_available = False
    PdfReader = None

# Silence protobuf warnings
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message=r"Protobuf gencode version .* is exactly one major version older than the runtime version .*",
)

# Build step: (re)create the collection and ingest documents
print("[index] Connecting to Weaviate at http://localhost:8080 ...")

# Use the new WeaviateManager for better compatibility
weaviate_manager = WeaviateManager()
if not weaviate_manager.connect():
    print("[index] Failed to connect to Weaviate")
    exit(1)

client = weaviate_manager.client

# Use the WeaviateManager to create collection with proper API handling
collection_name = "Question"
print(f"[index] Creating/recreating collection '{collection_name}' ...")

# Delete existing collection if it exists
weaviate_manager.delete_collection(collection_name)

# Create collection with proper API version handling
if weaviate_manager.api_version == "v4":
    vectorizer_config = Configure.Vectors.text2vec_ollama(
        api_endpoint="http://ollama:11434",
        model="nomic-embed-text",
    )
else:
    # For v3 API, we'll use "none" vectorizer
    vectorizer_config = {"vectorizer": "none"}

success = weaviate_manager.create_collection(
    collection_name=collection_name,
    vectorizer_config=vectorizer_config
)

if not success:
    print(f"[index] Failed to create collection '{collection_name}'")
    exit(1)

print(f"[index] Successfully created collection '{collection_name}'")

# Read local PDF and chunk into fixed 200-word segments
pdf_path = "main__.pdf"

if HAS_PYPDF and Path(pdf_path).exists():
    print(f"[index] Reading PDF: {pdf_path} ...")
    reader = PdfReader(pdf_path)
    num_pages = len(reader.pages)
    print(f"[index] PDF pages: {num_pages}")

    full_text_parts = []
    for page_idx, page in enumerate(reader.pages, start=1):
        text = page.extract_text() or ""
        full_text_parts.append(text)
        if page_idx % 5 == 0 or page_idx == num_pages:
            print(f"[index] Extracted page {page_idx}/{num_pages}")

    full_text = "\n".join(full_text_parts)
else:
    print(f"[index] PDF not found or pypdf not available, using sample text ...")
    # Use sample biology text for demonstration
    full_text = """
    Biology is the scientific study of life and living organisms. It encompasses various fields including molecular biology, genetics, ecology, and evolutionary biology.
    Cell biology focuses on the structure and function of cells, which are the basic units of life. Cells contain organelles that perform specific functions necessary for survival.
    Genetics is the study of heredity and the variation of inherited characteristics. DNA contains the genetic instructions for the development and function of living things.
    Ecology examines the relationships between organisms and their environment. It studies how organisms interact with each other and with their physical surroundings.
    Evolution is the process by which different kinds of living organisms develop and diversify from earlier forms during the history of the earth.
    Photosynthesis is the process by which plants and other organisms convert light energy into chemical energy that can be later released to fuel the organism's activities.
    The nervous system is a complex network of nerves and cells that carry messages to and from the brain and spinal cord to various parts of the body.
    Protein synthesis is the process by which cells build proteins. It involves transcription of DNA to RNA and translation of RNA to proteins.
    """

words = re.findall(r"\S+", full_text)
chunk_size = 200
chunks = [
    " ".join(words[i : i + chunk_size]) for i in range(0, len(words), chunk_size)
]
non_empty_chunks = [(i, t) for i, t in enumerate(chunks) if t.strip()]
num_chunks = len(non_empty_chunks)
print(f"[index] Total words: {len(words)} | Chunks (@{chunk_size} words): {num_chunks}")

# Prepare documents for ingestion
documents = []
for orig_idx, chunk_text in non_empty_chunks:
    documents.append({
        "text": chunk_text,
        "chunk_index": orig_idx,
        "source": pdf_path if HAS_PYPDF and Path(pdf_path).exists() else "sample_text",
        "document_type": "pdf" if HAS_PYPDF and Path(pdf_path).exists() else "text"
    })

print("[index] Ingesting chunks ...")

# Use the WeaviateManager for ingestion with proper API handling
num_success, num_failed = weaviate_manager.ingest_documents(
    documents=documents,
    collection_name=collection_name,
    batch_size=50
)

print(f"[index] Ingestion summary — success: {num_success}, failed: {num_failed}")
if num_failed > 0:
    print(f"[index] Warning: {num_failed} documents failed to ingest")

weaviate_manager.disconnect()
print("[index] Done.")